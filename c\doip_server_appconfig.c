/********************************************************************************
**
** 文件名:     doio_server_appconfig.h
** 版权所有:   (c) 2022-2028 厦门雅迅网络股份有限公司
** 文件描述:   doip服务端应用层信息配置
**
*********************************************************************************
**             修改历史记录
**===============================================================================
**| 日期       | 作者   |  修改记录
**===============================================================================
**| 2025/02/12 | yx |  创建该文件
**
*********************************************************************************/

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include "soad_internal.h"
#include "soad_configtypes.h"
#include "doip_server_appconfig.h"

static uint8 sg_tmp_doip_tester_count = DOIP_TESTER_COUNT;
static uint8 sg_tmp_doip_target_count = DOIP_TARGET_COUNT;
//static uint8 sg_tmp_doip_max_tester_connections = DOIP_MAX_TESTER_CONNECTIONS;
static uint8 sg_tmp_doip_routingactivation_count = DOIP_ROUTINGACTIVATION_COUNT;
static DoIp_RoutingActivationToTargetAddressMappingType *tmp_iDoIpRoutingActivationToTargetAddressMap = NULL;


/**********************************************************
 * 函数名称：DoIP_Server_AppConfig
 * 函数作用：服务端应用层信息配置
 * 函数入参：[in] app_soad_config:配置参数结构体
 * 函数返回值：成功返回0，失败返回-1
**********************************************************/
int DoIP_Server_AppConfig(APPCONFIG_SoAdDoIP_ConfigType* app_soad_config)
{
	int i = 0;
	int j = 0;
	int ret = -1;
	if(app_soad_config->vin_val == NULL){
		return -1;
	}
	
	/** 本设备地址赋值 **/
	if (app_soad_config->DoIpNodeLogicalAddress > 0)
	{
		gSoAdConfig.DoIpNodeLogicalAddress = app_soad_config->DoIpNodeLogicalAddress;
	}
	
	/** 允许测试设备的SA地址赋值 **/
	if (app_soad_config->DoIpTesters_num > 0)
	{
		sg_tmp_doip_tester_count = app_soad_config->DoIpTesters_num;
	}
	if (app_soad_config->DoIpTesters != NULL && app_soad_config->DoIpTargetAddresses_num > 0)
	{
		gSoAdConfig.DoIpTesters = (DoIp_TesterConfigType*)(app_soad_config->DoIpTesters);
	}
	
	/** 允许输入的TA地址赋值 **/
	if (app_soad_config->DoIpTargetAddresses_num > 0)
	{
		sg_tmp_doip_target_count = app_soad_config->DoIpTargetAddresses_num;
	}
	if (app_soad_config->DoIpTargetAddresses != NULL && app_soad_config->DoIpTargetAddresses_num > 0)
	{
		gSoAdConfig.DoIpTargetAddresses = (DoIp_TargetAddressConfigType*)app_soad_config->DoIpTargetAddresses;
	}

	
	/** 如果ta地址个数有变化，需重新生成‘DoIpRoutingActivationToTargetAddressMap’映射表 **/
	if (app_soad_config->DoIpTargetAddresses_num > 0 && app_soad_config->DoIpTargetAddresses_num != DOIP_TARGET_COUNT)
	{
		sg_tmp_doip_target_count = app_soad_config->DoIpTargetAddresses_num;
		int routeactiontotamap_size = sg_tmp_doip_target_count * sg_tmp_doip_routingactivation_count;
		if(tmp_iDoIpRoutingActivationToTargetAddressMap != NULL){
			free(tmp_iDoIpRoutingActivationToTargetAddressMap);
			tmp_iDoIpRoutingActivationToTargetAddressMap = NULL;
		}
		tmp_iDoIpRoutingActivationToTargetAddressMap = (DoIp_RoutingActivationToTargetAddressMappingType *)malloc(routeactiontotamap_size * (sizeof(DoIp_RoutingActivationToTargetAddressMappingType)));
		assert(tmp_iDoIpRoutingActivationToTargetAddressMap != NULL);
		
		for (i = 0; i < sg_tmp_doip_routingactivation_count; i++)
		{
			for (j = 0; j < sg_tmp_doip_target_count; j++)
			{
				tmp_iDoIpRoutingActivationToTargetAddressMap[i * sg_tmp_doip_target_count + j].routingActivation = i;
				tmp_iDoIpRoutingActivationToTargetAddressMap[i * sg_tmp_doip_target_count + j].target = j;
			}
		}
		gSoAdConfig.DoIpRoutingActivationToTargetAddressMap = tmp_iDoIpRoutingActivationToTargetAddressMap;
	}

	DOIP_CONFIG_PARAM_ST tmp_doip_config_param;
	tmp_doip_config_param.tester_count = sg_tmp_doip_tester_count;
	tmp_doip_config_param.target_count = sg_tmp_doip_target_count;
	tmp_doip_config_param.routingactivation_count = sg_tmp_doip_routingactivation_count;
	
	if (app_soad_config->vin_len == 17)
	{
		tmp_doip_config_param.vin_len = app_soad_config->vin_len;
		tmp_doip_config_param.vin_val = (uint8*)app_soad_config->vin_val;
	}
	
	if (app_soad_config->eid_len == 6)
	{
		tmp_doip_config_param.eid_len = app_soad_config->eid_len;
		tmp_doip_config_param.eid_val = app_soad_config->eid_val;
	}
	
	if (app_soad_config->gid_len == 6)
	{
		tmp_doip_config_param.gid_len = app_soad_config->gid_len;
		tmp_doip_config_param.gid_val = app_soad_config->gid_val;
	}
	
	if (app_soad_config->broadcast_len > 7)
	{
		tmp_doip_config_param.broadcast_len = app_soad_config->broadcast_len;
		tmp_doip_config_param.broadcast_address = app_soad_config->broadcast_address;
	}
	else
	{
		tmp_doip_config_param.broadcast_len = 0;
	}
	
	if (app_soad_config->multicast_len > 7)
	{
		tmp_doip_config_param.multicast_len = app_soad_config->multicast_len;
		tmp_doip_config_param.multicast_address = app_soad_config->multicast_address;
	}
	else
	{
		tmp_doip_config_param.multicast_len = 0;
	}

	if (app_soad_config->ethname_len)
	{
		tmp_doip_config_param.ethname_len = app_soad_config->ethname_len;
		tmp_doip_config_param.ethname = app_soad_config->ethname;
	}
	else
	{
		tmp_doip_config_param.ethname_len = 0;
	}
	
	ret = DoIP_ParameterConfig(tmp_doip_config_param);

	/** 时间参数相关配置 **/
	if(app_soad_config->SoAdDoIpTime != NULL){
		gSoAdConfig.DoIpConfig->DoIpAliveCheckresponseTimeMs = app_soad_config->SoAdDoIpTime->DoIpAliveCheckresponseTimeMs;
		gSoAdConfig.DoIpConfig->DoIpControlTimeoutMs = app_soad_config->SoAdDoIpTime->DoIpControlTimeoutMs;
		gSoAdConfig.DoIpConfig->DoIpGenericInactiveTimeMs = app_soad_config->SoAdDoIpTime->DoIpGenericInactiveTimeMs;
		gSoAdConfig.DoIpConfig->DoIpInitialInactiveTimeMs = app_soad_config->SoAdDoIpTime->DoIpInitialInactiveTimeMs;
		gSoAdConfig.DoIpConfig->DoipPowerModeCallback = NULL;
		gSoAdConfig.DoIpConfig->DoIpResponseTimeoutMs = app_soad_config->SoAdDoIpTime->DoIpResponseTimeoutMs;
		gSoAdConfig.DoIpConfig->DoIpVidAnnounceIntervalMs = app_soad_config->SoAdDoIpTime->DoIpVidAnnounceIntervalMs;
		gSoAdConfig.DoIpConfig->DoIpVidAnnounceMaxWaitMs = app_soad_config->SoAdDoIpTime->DoIpVidAnnounceMaxWaitMs;
		gSoAdConfig.DoIpConfig->DoIpVidAnnounceMinWaitMs = app_soad_config->SoAdDoIpTime->DoIpVidAnnounceMinWaitMs;
		gSoAdConfig.DoIpConfig->DoIpVidAnnounceNum = app_soad_config->SoAdDoIpTime->DoIpVidAnnounceNum;
	}
	
	
	return ret;
}

/**********************************************************
 * 函数名称：DoIP_Server_AppFunConfig
 * 函数作用：服务端应用层回调函数信息配置
 * 函数入参：[in] app_soad_funconfig:配置参数结构体
 * 函数返回值：成功返回0，失败返回-1
**********************************************************/
int DoIP_Server_AppFunConfig(APPFUNCONFIG_SoAd_ConfigType* app_soad_funconfig)
{
	int ret = -1;
	int i = 0;
	DOIP_CONFIG_FUNCALLBACK_ST tmp_config_funcallback;

	tmp_config_funcallback.debugstatus = app_soad_funconfig->debugstatus;
	tmp_config_funcallback.soaddoip_debugmsg = (gSoadDoipDebugMsg)app_soad_funconfig->soaddoip_debugmsg;
	tmp_config_funcallback.pDoIPServerDiagnosticMsg = app_soad_funconfig->pDoIPServerDiagnosticMsg;
	tmp_config_funcallback.DoipEventNotifier = app_soad_funconfig->DoipEventNotifier;
	tmp_config_funcallback.SetDgsChnStatus = app_soad_funconfig->SetDgsChnStatus;
	/** 路由激活的种类个数 **/
	if(app_soad_funconfig->routingactivenum > 0){
		sg_tmp_doip_routingactivation_count = app_soad_funconfig->routingactivenum;
		if(sg_tmp_doip_routingactivation_count != DOIP_ROUTINGACTIVATION_COUNT){
			gSoAdConfig.DoIpRoutingActivations = (DoIp_RoutingActivationConfigType*)calloc(sg_tmp_doip_routingactivation_count,sizeof(DoIp_RoutingActivationConfigType));
		}
		for(i = 0; i < sg_tmp_doip_routingactivation_count; i++){
			gSoAdConfig.DoIpRoutingActivations[i].activationNumber = app_soad_funconfig->routingactivearry[i].activationNumber;
			//printf("activationNumber = %d\n",gSoAdConfig.DoIpRoutingActivations[i].activationNumber);
			gSoAdConfig.DoIpRoutingActivations[i].authenticationCallback = app_soad_funconfig->routingactivearry[i].authenticationCallback;
			gSoAdConfig.DoIpRoutingActivations[i].confirmationCallback = app_soad_funconfig->routingactivearry[i].confirmationCallback;
		}
		
	}
	
	ret = DoIP_CallbackFUNConfig(tmp_config_funcallback);
	return ret;
}

/**********************************************************
 * 函数名称：DoIP_Server_AppDeconfig
 * 函数作用：服务端应用层取消配置退出
 * 函数入参：无
 * 函数返回值：成功返回0，失败返回-1
**********************************************************/
int DoIP_Server_AppDeconfig(void)
{
	if (sg_tmp_doip_target_count > 0 && sg_tmp_doip_target_count != DOIP_TARGET_COUNT){
		if(tmp_iDoIpRoutingActivationToTargetAddressMap != NULL){
			free(tmp_iDoIpRoutingActivationToTargetAddressMap);
			tmp_iDoIpRoutingActivationToTargetAddressMap = NULL;
		}
	}
	
	if(sg_tmp_doip_routingactivation_count != DOIP_ROUTINGACTIVATION_COUNT){
		if(gSoAdConfig.DoIpRoutingActivations){
			free(gSoAdConfig.DoIpRoutingActivations);
		}
	}
	return 0;
}
