# DoIP (Diagnostics over Internet Protocol) 设计方案

## 1. 项目概述

### 1.1 项目简介
本项目实现了基于ISO 13400标准的DoIP (Diagnostics over Internet Protocol) 服务端，支持通过以太网进行车辆诊断通信。系统采用分层架构设计，提供完整的DoIP协议栈实现。

### 1.2 版本信息
- **版本号**: V1.0.2
- **协议版本**: DoIP 0x02 (支持ISO 13400-2:2019)
- **开发语言**: C语言
- **目标平台**: Linux (支持多种网络接口)

### 1.3 主要特性
- 支持UDP和TCP双协议栈
- 车辆识别与声明机制
- 路由激活与管理
- 诊断消息传输
- 在线检查与超时管理
- 多播/广播支持
- 灵活的配置管理

## 2. 系统架构

### 2.1 分层架构设计

```
┌─────────────────────────────────────────────────────────┐
│                   应用层 (Application Layer)              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────┐  │
│  │   诊断服务      │  │   配置管理      │  │  事件通知 │  │
│  │  (Diagnostic)   │  │ (Configuration) │  │ (Events) │  │
│  └─────────────────┘  └─────────────────┘  └──────────┘  │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                DoIP协议层 (DoIP Protocol Layer)          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────┐  │
│  │   消息处理      │  │   路由管理      │  │  状态管理 │  │
│  │ (Message Handle)│  │ (Routing Mgmt)  │  │ (State)  │  │
│  └─────────────────┘  └─────────────────┘  └──────────┘  │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│              SoAd层 (Socket Adapter Layer)              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────┐  │
│  │   套接字管理    │  │   数据传输      │  │  连接管理 │  │
│  │ (Socket Admin)  │  │ (Data Transfer) │  │(Connection)│  │
│  └─────────────────┘  └─────────────────┘  └──────────┘  │
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                传输层 (Transport Layer)                  │
│              TCP/UDP + Socket API                       │
└─────────────────────────────────────────────────────────┘
```

### 2.2 核心模块组成

#### 2.2.1 DoIP服务器模块 (doip_server)
- **文件**: `c/doip_server.c`, `h/doip_server.h`
- **功能**: 提供DoIP服务的主要接口
- **主要接口**:
  - `DoIP_Server_Init()`: 服务初始化
  - `DoIP_Server_DeInit()`: 服务去初始化
  - `DoIP_Server_Diagnostic_msg()`: 诊断消息处理

#### 2.2.2 SoAd套接字适配层 (soad)
- **文件**: `src_c/soad.c`, `src_h/soad.h`
- **功能**: 套接字管理和网络通信抽象
- **主要功能**:
  - 套接字创建、绑定、监听
  - TCP/UDP消息收发
  - 连接状态管理
  - 网络事件处理

#### 2.2.3 DoIP协议处理模块 (soad_doip)
- **文件**: `src_c/soad_doip.c`
- **功能**: DoIP协议消息处理核心
- **主要功能**:
  - DoIP消息解析与构造
  - 车辆识别处理
  - 路由激活管理
  - 诊断消息路由
  - 在线检查机制

#### 2.2.4 配置管理模块
- **文件**: `src_c/doip_cfg.c`, `c/doip_server_appconfig.c`
- **功能**: 系统配置管理
- **配置内容**:
  - 套接字配置
  - DoIP参数配置
  - 超时参数配置
  - 地址映射配置

## 3. 核心数据结构

### 3.1 套接字连接配置
```c
typedef struct {
    uint16 SocketId;                    // 套接字ID
    char* SocketLocalIpAddress;         // 本地IP地址
    uint16 SocketLocalPort;             // 本地端口 (13400)
    char* SocketRemoteIpAddress;        // 远程IP地址
    uint16 SocketRemotePort;            // 远程端口
    SoAd_SocketProtocolType SocketProtocol; // TCP/UDP协议类型
    boolean SocketTcpInitiate;          // TCP主动连接标志
    boolean SocketTcpNoDelay;           // TCP无延迟标志
    boolean SocketUdpListenOnly;        // UDP仅监听标志
    SoAd_AutosarConnectorType AutosarConnectorType; // 连接器类型
    boolean PduHeaderEnable;            // PDU头使能
    boolean SocketAutosarApi;           // AUTOSAR API使能
    boolean ResourceManagementEnable;   // 资源管理使能
    boolean PduProvideBufferEnable;     // PDU缓冲区提供使能
} SoAd_SocketConnectionType;
```

### 3.2 DoIP配置结构
```c
typedef struct {
    uint32 DoIpAliveCheckresponseTimeMs;    // 在线检查响应超时 (500ms)
    uint32 DoIpControlTimeoutMs;            // 控制超时 (2000ms)
    uint32 DoIpGenericInactiveTimeMs;       // 通用非活动超时 (5分钟)
    uint8* DoIpHostNameOpt;                 // 主机名选项
    uint32 DoIpInitialInactiveTimeMs;       // 初始非活动超时 (2000ms)
    uint32 DoIpResponseTimeoutMs;           // 响应超时 (500ms)
    uint32 DoIpVidAnnounceIntervalMs;       // 车辆识别声明间隔 (500ms)
    uint32 DoIpVidAnnounceMaxWaitMs;        // 车辆识别声明最大等待 (1500ms)
    uint32 DoIpVidAnnounceMinWaitMs;        // 车辆识别声明最小等待 (0ms)
    uint32 DoIpVidAnnounceNum;              // 车辆识别声明次数 (3次)
    SoAd_CallbackDoIpPowerModeFncType DoipPowerModeCallback; // 电源模式回调
} SoAd_DoIpConfigType;
```

### 3.3 套接字状态管理
```c
typedef struct {
    uint8 SocketNr;                         // 套接字编号
    SocketStateType SocketState;            // 套接字状态
    boolean SocketProtocolIsTcp;            // 是否TCP协议
    const SoAd_SocketConnectionType* SocketConnectionRef; // 连接配置引用
    const SoAd_SocketRouteType* SocketRouteRef;          // 路由配置引用
    uint32 RemoteIpAddress;                 // 远程IP地址
    uint16 RemotePort;                      // 远程端口
    int SocketHandle;                       // 套接字句柄
    int ConnectionHandle;                   // 连接句柄
    uint32 TcpinitialInactivityTimer;       // TCP初始非活动定时器
} SocketAdminType;
```

### 3.4 DoIP连接状态
```c
typedef struct {
    uint16 sockNr;                          // 套接字编号
    uint16 sa;                              // 诊断仪逻辑地址
    uint8 activationType;                   // 路由激活类型
    SoAd_ArcDoIpSocketStateType socketState; // 套接字注册状态
    uint32 initialInactivityTimer;          // 初始非活动定时器
    uint32 generalInactivityTimer;          // 通用非活动定时器
    uint32 aliveCheckTimer;                 // 在线检查定时器
    boolean authenticated;                  // 认证状态
    boolean confirmed;                      // 确认状态
    boolean awaitingAliveCheckResponse;     // 等待在线检查响应标志
    uint16 targetIndex;                     // 目标索引
    PduStatusType pduStatus;                // PDU状态
} DoIp_ArcDoIpSocketStatusType;
```

## 4. DoIP协议消息类型

### 4.1 消息类型定义
```c
typedef enum {
    PAYLOAD_TYPE_NACK = 0x0000,                 // DoIP首部否定响应
    PAYLOAD_TYPE_IDREQUEST = 0x0001,            // 车辆识别请求
    PAYLOAD_TYPE_IDREQUEST_BY_EID = 0x0002,     // 带EID的车辆识别请求
    PAYLOAD_TYPE_IDREQUEST_BY_VIN = 0x0003,     // 带VIN的车辆识别请求
    PAYLOAD_TYPE_ANNOUNCEMENT = 0x0004,          // 车辆声明/车辆识别响应
    PAYLOAD_TYPE_ROUTING_ACTIVATION = 0x0005,    // 路由激活请求
    PAYLOAD_TYPE_ALIVE_CHECK_REQUEST = 0x0007,   // 在线检查请求
    PAYLOAD_TYPE_ALIVE_CHECK_RESPONSE = 0x0008,  // 在线检查响应
    PAYLOAD_TYPE_STATUS_REQUEST = 0x4001,        // DoIP实体状态请求
    PAYLOAD_TYPE_POWERMOE_REQUEST = 0x4003,      // 诊断电源模式请求
    PAYLOAD_TYPE_DIAGNOSTIC_MESSAGE = 0x8001,    // 诊断报文
} PAYLOAD_TYPE_E;
```

### 4.2 DoIP消息格式
```
DoIP Header (8 bytes):
┌─────────────┬─────────────┬─────────────┬─────────────┐
│ Protocol    │ Inverse     │ Payload     │ Payload     │
│ Version     │ Protocol    │ Type        │ Length      │
│ (1 byte)    │ Version     │ (2 bytes)   │ (4 bytes)   │
│             │ (1 byte)    │             │             │
└─────────────┴─────────────┴─────────────┴─────────────┘

Payload Data (Variable length):
┌─────────────────────────────────────────────────────────┐
│                    Payload Data                         │
│                 (Length bytes)                          │
└─────────────────────────────────────────────────────────┘
```

## 5. 主要功能流程

### 5.1 系统初始化流程
```
1. DoIP_Server_Init()
   ├── 解析配置参数 (JSON文件或结构体)
   ├── 初始化回调函数
   ├── 配置DoIP参数
   ├── 调用SoAd_Doip_Init()
   │   ├── 初始化套接字管理
   │   ├── 创建UDP套接字 (端口13400)
   │   ├── 创建TCP监听套接字 (端口13400)
   │   ├── 配置多播组加入
   │   ├── 初始化DoIP状态机
   │   └── 启动主循环线程
   └── 返回初始化结果
```

### 5.2 车辆识别流程
```
UDP接收车辆识别请求:
1. 接收UDP消息 (端口13400)
2. 解析DoIP头部
3. 验证协议版本
4. 根据消息类型处理:
   ├── PAYLOAD_TYPE_IDREQUEST: 通用车辆识别
   ├── PAYLOAD_TYPE_IDREQUEST_BY_EID: 基于EID识别
   └── PAYLOAD_TYPE_IDREQUEST_BY_VIN: 基于VIN识别
5. 构造车辆识别响应 (PAYLOAD_TYPE_ANNOUNCEMENT)
6. 通过UDP广播发送响应
```

### 5.3 路由激活流程
```
TCP路由激活处理:
1. 接收TCP连接请求
2. 接收路由激活请求 (PAYLOAD_TYPE_ROUTING_ACTIVATION)
3. 验证源地址 (SA) 合法性
4. 检查路由激活类型支持
5. 分配套接字资源
6. 执行认证检查 (如需要)
7. 执行确认检查 (如需要)
8. 构造路由激活响应
9. 发送响应并更新连接状态
10. 启动非活动定时器
```

### 5.4 诊断消息传输流程
```
诊断消息处理:
1. 接收诊断消息 (PAYLOAD_TYPE_DIAGNOSTIC_MESSAGE)
2. 解析SA和TA地址
3. 验证地址映射合法性
4. 检查连接状态
5. 提取诊断数据
6. 调用应用层回调函数
7. 处理诊断响应 (如有)
8. 构造诊断响应消息
9. 发送响应到诊断仪
```

### 5.5 在线检查机制
```
在线检查流程:
1. 定时器触发在线检查
2. 构造在线检查请求 (PAYLOAD_TYPE_ALIVE_CHECK_REQUEST)
3. 发送到所有活动连接
4. 设置等待响应标志
5. 启动响应超时定时器
6. 接收在线检查响应 (PAYLOAD_TYPE_ALIVE_CHECK_RESPONSE)
7. 验证响应SA地址
8. 重置非活动定时器
9. 清除等待响应标志
10. 超时处理: 关闭无响应连接
```

## 6. 接口定义

### 6.1 主要API接口

#### 6.1.1 初始化接口
```c
/**
 * DoIP服务接口初始化
 * @param pAppfunSoadConfig: 回调信息相关配置
 * @param pAppSoadConfig: app相关配置信息
 * @param parampath: JSON配置文件路径
 * @return: 成功返回0, 失败返回-1
 */
int DoIP_Server_Init(APPFUNCONFIG_SoAd_ConfigType *pAppfunSoadConfig,
                     APPCONFIG_SoAdDoIP_ConfigType *pAppSoadConfig,
                     char* parampath);
```

#### 6.1.2 去初始化接口
```c
/**
 * DoIP服务接口去初始化
 * @return: 成功返回0, 失败返回小于0
 */
int DoIP_Server_DeInit(void);
```

#### 6.1.3 诊断消息发送接口
```c
/**
 * 诊断消息发送接口
 * @param tpsocknr: 套接字句柄
 * @param ptr: 数据缓冲区
 * @param len: 数据长度
 * @return: 成功返回发送字节数, 失败返回负值
 */
int SoAdTp_Transmit_Dianostic(uint16 tpsocknr, uint8* ptr, uint16 len);
```

### 6.2 回调函数接口

#### 6.2.1 诊断数据回调
```c
/**
 * 诊断数据接收回调函数
 * @param recinfo: DoIP接收数据信息
 * @param recbuf: 接收数据缓冲区
 * @param reclen: 接收数据长度
 */
typedef void(*gappcallback_handle)(doiprecinfo_st recinfo, 
                                   unsigned char *recbuf, 
                                   unsigned int reclen);
```

#### 6.2.2 事件通知回调
```c
/**
 * DoIP事件通知函数
 * @param event: 事件类型
 * @param data: 附加数据
 */
typedef void(*gappDoipEventNotifier)(unsigned char event, void *data);
```

#### 6.2.3 通道状态控制回调
```c
/**
 * 以太网诊断通道占用或释放状态控制
 * @param status: 0x01-通道占用, 0x02-通道释放
 * @return: 成功返回0, 失败返回-1
 */
typedef int(*gappSetDgsChnStatus)(char status);
```

#### 6.2.4 调试信息输出回调
```c
/**
 * 调试信息输出函数
 * @param level: 调试级别
 * @param format: 格式化字符串
 * @param ...: 可变参数
 */
typedef void (*gappSoadDoipDebugMsg)(APPSOADDOIP_DEBUG_ENUM level,
                                     const char* format, ...);
```

## 7. 配置管理

### 7.1 静态配置 (doip_cfg.c)
系统提供默认的静态配置，包括:
- 套接字连接配置 (UDP端口13400, TCP端口13400)
- DoIP超时参数配置
- 目标地址和测试设备地址配置
- 路由激活配置

### 7.2 动态配置 (JSON文件)
支持通过JSON配置文件进行动态配置:
```json
{
    "VIN": "doip-vin012345679",
    "EID": 2,
    "GID": 2,
    "LogicalAddress": 37,
    "broadcastaddr": "***************",
    "multicastaddr": "**********",
    "ethname": "eth0",
    "DoipTesterSaList": [
        {"address": 3712, "numbytes": 0},
        {"address": 3716, "numbytes": 0}
    ],
    "DoipTaList": [
        {"addressValue": 37, "txPdu": 0, "rxPdu": 0},
        {"addressValue": 58368, "txPdu": 0, "rxPdu": 0}
    ],
    "ResponseTimeoutMs": 500,
    "GenericInactiveTimeMs": 300000,
    "InitialInactiveTimeMs": 2000,
    "VidAnnounceNum": 3,
    "VidAnnounceIntervalMs": 500,
    "VidAnnounceMaxWaitMs": 1500,
    "VidAnnounceMinWaitMs": 0,
    "AliveCheckresponseTimeMs": 500,
    "DoIpControlTimeoutMs": 2000
}
```

### 7.3 配置参数说明

#### 7.3.1 基本参数
- **VIN**: 车辆识别号 (17字节)
- **EID**: 实体标识符 (6字节)
- **GID**: 组标识符 (6字节)
- **LogicalAddress**: DoIP实体逻辑地址

#### 7.3.2 网络参数
- **broadcastaddr**: 广播地址
- **multicastaddr**: 多播地址 (默认**********)
- **ethname**: 网络接口名称

#### 7.3.3 地址配置
- **DoipTesterSaList**: 允许连接的诊断仪SA地址列表
- **DoipTaList**: 支持的目标地址TA列表

#### 7.3.4 超时参数
- **ResponseTimeoutMs**: 响应超时 (500ms)
- **GenericInactiveTimeMs**: 通用非活动超时 (5分钟)
- **InitialInactiveTimeMs**: 初始非活动超时 (2秒)
- **AliveCheckresponseTimeMs**: 在线检查响应超时 (500ms)
- **VidAnnounceNum**: 车辆识别声明次数 (3次)
- **VidAnnounceIntervalMs**: 声明间隔 (500ms)

## 8. 错误处理与调试

### 8.1 错误码定义
系统定义了完整的错误码体系:
- 初始化错误: -1 到 -4
- 网络错误: 套接字创建、绑定、连接失败
- 协议错误: 消息格式错误、版本不匹配
- 超时错误: 响应超时、连接超时

### 8.2 调试级别
```c
typedef enum {
    APPSOADDOIP_DEBUG_NONE = 0x0,      // 无调试信息
    APPSOADDOIP_DEBUG_ERROR = 0x01,    // 错误信息
    APPSOADDOIP_DEBUG_WARN = 0x02,     // 警告信息
    APPSOADDOIP_DEBUG_INFO = 0x04,     // 信息输出
} APPSOADDOIP_DEBUG_ENUM;
```

### 8.3 日志输出
系统提供灵活的日志输出机制，支持:
- 分级日志输出
- 自定义日志回调函数
- 运行时日志级别控制
- 详细的错误信息和状态跟踪

## 9. 性能特性

### 9.1 并发处理
- 支持多个诊断仪同时连接
- 异步消息处理机制
- 线程安全的状态管理
- 高效的套接字事件处理 (epoll)

### 9.2 资源管理
- 动态内存管理
- 缓冲区池化机制
- 连接资源限制
- 超时自动清理

### 9.3 网络优化
- TCP_NODELAY选项支持
- 套接字重用机制
- 多播组管理
- 广播优化

## 10. 测试与验证

### 10.1 测试框架
项目提供完整的测试代码 (`test_code/`):
- 单元测试
- 集成测试
- 性能测试
- 兼容性测试

### 10.2 测试用例
- DoIP协议一致性测试
- 网络连接稳定性测试
- 超时机制验证
- 错误处理测试
- 多客户端并发测试

## 11. 部署与维护

### 11.1 编译构建
```bash
# 编译主库
make

# 编译测试程序
cd test_code
make
```

### 11.2 运行要求
- Linux操作系统
- 网络接口支持
- 端口13400可用
- 足够的内存资源

### 11.3 配置部署
1. 准备JSON配置文件
2. 设置网络接口和路由
3. 配置防火墙规则
4. 启动DoIP服务

### 11.4 监控维护
- 连接状态监控
- 性能指标收集
- 日志分析
- 故障诊断

## 12. 扩展性设计

### 12.1 模块化设计
系统采用高度模块化的设计，便于:
- 功能模块独立开发
- 接口标准化
- 组件可替换性
- 系统可扩展性

### 12.2 配置灵活性
- 支持静态和动态配置
- 运行时参数调整
- 多种配置源支持
- 配置热更新机制

### 12.3 协议扩展
- 支持新的DoIP消息类型
- 自定义协议扩展
- 版本兼容性管理
- 向后兼容保证

---

**文档版本**: V1.0  
**最后更新**: 2024年  
**维护团队**: 厦门雅迅网络股份有限公司
