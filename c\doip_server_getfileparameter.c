/********************************************************************************
**
** 文件名:     doip_server_getfileparameter.c
** 版权所有:   (c) 2022-2028 厦门雅迅网络股份有限公司
** 文件描述:   doip服务端获取配置文件参数
**
*********************************************************************************
**             修改历史记录
**===============================================================================
**| 日期       | 作者   |  修改记录
**===============================================================================
**| 2025/02/18 | yx |  创建该文件
**
*********************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include "cjson.h"
#include "doip_server_getfileparameter.h"

void free_config(Config *config) {
    if (config) {
        free(config->VIN);
        free(config->broadcastaddr);
        free(config->multicastaddr);
        free(config->ethname);
        free(config->DoipTesterSaList);
        free(config->DoipTaList);
        free(config);
    }
}

Config* parse_config(const char *json_str) {
    cJSON *root = cJSON_Parse(json_str);
    if (!root) {
        fprintf(stderr, "JSON parse error: %s\n", cJSON_GetErrorPtr());
        return NULL;
    }

    Config *config = calloc(1, sizeof(Config));
    if (!config) {
        cJSON_Delete(root);
        return NULL;
    }

    // 解析字符串字段并检查内存分配
    #define PARSE_STRING_FIELD(field) do { \
        item = cJSON_GetObjectItemCaseSensitive(root, #field); \
        if (cJSON_IsString(item)) { \
            config->field = strdup(item->valuestring); \
            if (!config->field) { \
                fprintf(stderr, "Memory allocation failed for " #field "\n"); \
                goto error; \
            } \
        } else { \
            fprintf(stderr, "Missing or invalid field: " #field "\n"); \
            goto error; \
        } \
    } while(0)

    // 解析数值字段
    #define PARSE_NUMBER_FIELD(field, type) do { \
        item = cJSON_GetObjectItemCaseSensitive(root, #field); \
        if (cJSON_IsNumber(item)) { \
            config->field = (type)item->valuedouble; \
        } else { \
            fprintf(stderr, "Missing or invalid field: " #field "\n"); \
            goto error; \
        } \
    } while(0)

    cJSON *item = NULL;
    
    PARSE_STRING_FIELD(VIN);
    PARSE_NUMBER_FIELD(EID, long long);
    PARSE_NUMBER_FIELD(GID, long long);
    PARSE_STRING_FIELD(broadcastaddr);
    PARSE_STRING_FIELD(multicastaddr);
    PARSE_STRING_FIELD(ethname);
    PARSE_NUMBER_FIELD(LogicalAddress, int);

    // 解析DoipTesterSaList
    cJSON *sa_list = cJSON_GetObjectItemCaseSensitive(root, "DoipTesterSaList");
    if (cJSON_IsArray(sa_list)) {
        config->DoipTesterSaCount = cJSON_GetArraySize(sa_list);
        config->DoipTesterSaList = calloc(config->DoipTesterSaCount, sizeof(DoipTesterSaEntry));
        if (!config->DoipTesterSaList) {
            fprintf(stderr, "Memory allocation failed for DoipTesterSaList\n");
            goto error;
        }
        
        for (int i = 0; i < config->DoipTesterSaCount; i++) {
            cJSON *obj = cJSON_GetArrayItem(sa_list, i);
            cJSON *addr = cJSON_GetObjectItemCaseSensitive(obj, "address");
            cJSON *bytes = cJSON_GetObjectItemCaseSensitive(obj, "numbytes");
            
            if (!cJSON_IsNumber(addr) || !cJSON_IsNumber(bytes)) {
                fprintf(stderr, "Invalid type in DoipTesterSaList entry\n");
                goto error;
            }
            
            config->DoipTesterSaList[i].address = addr->valueint;
            config->DoipTesterSaList[i].numbytes = bytes->valueint;
        }
    } else {
        fprintf(stderr, "Missing or invalid DoipTesterSaList\n");
        goto error;
    }

    // 解析DoipTaList
    cJSON *ta_list = cJSON_GetObjectItemCaseSensitive(root, "DoipTaList");
    if (cJSON_IsArray(ta_list)) {
        config->DoipTaCount = cJSON_GetArraySize(ta_list);
        config->DoipTaList = calloc(config->DoipTaCount, sizeof(DoipTaEntry));
        if (!config->DoipTaList) {
            fprintf(stderr, "Memory allocation failed for DoipTaList\n");
            goto error;
        }
        
        for (int i = 0; i < config->DoipTaCount; i++) {
            cJSON *obj = cJSON_GetArrayItem(ta_list, i);
            cJSON *addr = cJSON_GetObjectItemCaseSensitive(obj, "addressValue");
            cJSON *tx = cJSON_GetObjectItemCaseSensitive(obj, "txPdu");
            cJSON *rx = cJSON_GetObjectItemCaseSensitive(obj, "rxPdu");
            
            if (!cJSON_IsNumber(addr) || !cJSON_IsNumber(tx) || !cJSON_IsNumber(rx)) {
                fprintf(stderr, "Invalid type in DoipTaList entry\n");
                goto error;
            }
            
            config->DoipTaList[i].addressValue = addr->valueint;
            config->DoipTaList[i].txPdu = tx->valueint;
            config->DoipTaList[i].rxPdu = rx->valueint;
        }
    } else {
        fprintf(stderr, "Missing or invalid DoipTaList\n");
        goto error;
    }

    // 解析超时参数
    #define PARSE_TIMEOUT_FIELD(field) PARSE_NUMBER_FIELD(field, int)
    PARSE_TIMEOUT_FIELD(ResponseTimeoutMs);
    PARSE_TIMEOUT_FIELD(GenericInactiveTimeMs);
    PARSE_TIMEOUT_FIELD(InitialInactiveTimeMs);
    PARSE_TIMEOUT_FIELD(VidAnnounceNum);
    PARSE_TIMEOUT_FIELD(VidAnnounceIntervalMs);
    PARSE_TIMEOUT_FIELD(VidAnnounceMaxWaitMs);
    PARSE_TIMEOUT_FIELD(VidAnnounceMinWaitMs);
    PARSE_TIMEOUT_FIELD(AliveCheckresponseTimeMs);
    PARSE_TIMEOUT_FIELD(DoIpControlTimeoutMs);

    cJSON_Delete(root);
    return config;

error:
    free_config(config);
    cJSON_Delete(root);
    return NULL;
}

Config* parse_config_from_file(const char *filename) {
    FILE *file = fopen(filename, "rb");
    if (!file) {
        perror("Failed to open file");
        return NULL;
    }

    if (fseek(file, 0, SEEK_END) != 0) {
        perror("Failed to seek file");
        fclose(file);
        return NULL;
    }

    long file_size = ftell(file);
    if (file_size <= 0) {
        fprintf(stderr, "Invalid file size\n");
        fclose(file);
        return NULL;
    }

    rewind(file);

    char *json_str = malloc(file_size + 1);
    if (!json_str) {
        fprintf(stderr, "Failed to allocate memory for JSON string\n");
        fclose(file);
        return NULL;
    }

    size_t bytes_read = fread(json_str, 1, file_size, file);
    fclose(file);

    if (bytes_read != (size_t)file_size) {
        fprintf(stderr, "File read error: expected %ld bytes, got %zu\n", file_size, bytes_read);
        free(json_str);
        return NULL;
    }

    json_str[file_size] = '\0';
    Config *config = parse_config(json_str);
    free(json_str);
    return config;
}

#if 0
int main() {
    
    // 新增文件解析测试
    Config *file_config = parse_config_from_file("config.json");
    if (file_config) {
        printf("File config parsed successfully\n");
        free_config(file_config);
    } else {
        printf("Failed to parse config from file\n");
    }

    return 0;
}
#endif

