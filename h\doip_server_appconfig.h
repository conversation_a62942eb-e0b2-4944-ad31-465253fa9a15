/********************************************************************************
**
** 文件名:     doio_server_appconfig.h
** 版权所有:   (c) 2022-2028 厦门雅迅网络股份有限公司
** 文件描述:   doip服务端应用层信息配置
**
*********************************************************************************
**             修改历史记录
**===============================================================================
**| 日期       | 作者   |  修改记录
**===============================================================================
**| 2025/02/12 | yx |  创建该文件
**
*********************************************************************************/
#ifndef __DOIP_SERVER_APPCONFIG_H__
#define __DOIP_SERVER_APPCONFIG_H__

#ifdef __cplusplus
extern "C"
{
#endif

typedef struct {
	unsigned short address;                            /** 允许链接设备的源地址信息 **/
	unsigned short numBytes;                           /** 该值暂时可以填充0 **/
} APPCONFIG_DoIp_TesterConfigType;

typedef struct {
	unsigned short addressValue;  //ta
	unsigned short txPdu;
	unsigned short rxPdu;
} APPCONFIG_DoIp_TargetAddressConfigType;

typedef void (*APPDoIp_AuthenticationCallbackType)(void);
typedef void (*APPDoIp_ConfirmationCallbackType)(void);

typedef struct {
	unsigned char activationNumber;  //activationType
	APPDoIp_AuthenticationCallbackType authenticationCallback;     /** 该值暂时可以填充0 **/
	APPDoIp_ConfirmationCallbackType confirmationCallback;         /** 该值暂时可以填充0 **/
} APPCONFIG_DoIp_RoutingActivationConfigType;

typedef struct {
	unsigned int		DoIpAliveCheckresponseTimeMs;	/** @req SOAD051_Conf */
	unsigned int		DoIpControlTimeoutMs;			/** @req SOAD065_Conf */
	unsigned int		DoIpGenericInactiveTimeMs;		/** @req SOAD052_Conf */
	unsigned int		DoIpInitialInactiveTimeMs;		/** @req SOAD054_Conf */
	unsigned int		DoIpResponseTimeoutMs;			/** @req SOAD066_Conf */
	unsigned int		DoIpVidAnnounceIntervalMs;		/** @req SOAD055_Conf */
	unsigned int		DoIpVidAnnounceMaxWaitMs;		/** @req SOAD056_Conf */
	unsigned int		DoIpVidAnnounceMinWaitMs;		/** @req SOAD057_Conf */
	unsigned int		DoIpVidAnnounceNum;				/** @req SOAD058_Conf */
} APPCONFIG_SoAdDoIpTimeConfigType;	/** @req SOAD050_Conf */


/** DoIP接收信息 **/
typedef struct DOIP_RECTYPE_INFO_ST
{
	unsigned short sa;
	unsigned short ta;
	unsigned char  chn;
	unsigned short sockNr;    /** 句柄序号 **/
} doiprecinfo_st;

/** DoIP发送信息 **/
typedef struct DOIP_SNDTYPE_INFO_ST
{
	unsigned short sa;
	unsigned short ta;
	unsigned short sockNr;    /** 句柄序号 **/
} doipsndinfo_st;

typedef enum doip_server_event_
{
	EVENT_CONNECTION_SUCCESSFUL,	/* 连接成功 */
	EVENT_ROUTING_ACTIVATION,		/* 路由激活 */
} EDoipServerEvent;
	
/**********************************************************
 * 函数名称:	gappcallback_handle
 * 函数作用:	doip接收数据回调函数指针
 * 函数参数:	[out] recinfo:		doip接收数据信息
 * 函数参数:	[out] recbuf:		为输出数据缓冲区
 * 函数参数:	[out] reclen:		为输出数据长度
 * 函数返回:	无
 * 注意事项:	如果注册了该接口，则源数据直接从此接口发送，
 *				且 sal_sdpe_doip_rec_inddata_register_callback 注册的回调函数失效
**********************************************************/
typedef void(*gappcallback_handle)(doiprecinfo_st , unsigned char *, unsigned int);

/**********************************************************
 * 函数名称:	gappDoipEventNotifier
 * 函数作用:	DoIP事件通知函数指针
 * 函数参数:	[in] event:		事件类型
 * 函数参数:	[in] data:		附加数据
 * 函数返回:	无
**********************************************************/
typedef void(*gappDoipEventNotifier)(unsigned char event, void *data);

/**********************************************************
 * 函数名称:	gappSetDgsChnStatus
 * 函数作用:	DoIP设置以太网诊断通道占用或释放状态
 * 函数参数:	[in] sync:		0x01:表示以太网通道占用，0x02:表示以太网通道释放
 * 函数返回:	占用或释放成功返回为0，失败返回-1
**********************************************************/
typedef int(*gappSetDgsChnStatus)(char status);

/**********************************************************
 * 函数名称:	gDoIPServerDiagnosticMsg
 * 函数作用:	诊断消息接收处理接口
 * 函数参数:	[in] socknr:	套接字句柄
 * 函数参数:	[in] sa:		接收到的sa
 * 函数参数:	[in] ta:		接收到的ta
 * 函数参数:	[in] channel:	接收到消息的通道
 * 函数参数:	[in] recv_buf:	接收到的消息缓冲区
 * 函数参数:	[in] recv_len:	接收到的消息大小
 * 函数返回:	成功返回0, 失败返回-1
**********************************************************/
typedef char (*gappDoIPServerDiagnosticMsg)(unsigned short socknr, unsigned short sa, \
		unsigned short ta, unsigned char channel, unsigned char *recv_buf, unsigned short recv_len);

typedef struct{
	unsigned char                                   vin_len;                       /** VIN长度 **/                      
	char                                            *vin_val;                      /** VIN值地址, 不允许使用局部变量的地址 **/
	unsigned char                                   eid_len;                       /** EID长度 **/
	long long                                       eid_val;                       /** EID值 **/
	unsigned char                                   gid_len;                       /** GID长度 **/
	long long                                       gid_val;                       /** GID值 **/
	unsigned short 						            DoIpNodeLogicalAddress;        /** 本设备源地址 **/
	unsigned char                                   DoIpTesters_num;               /** 允许访问的SA地址个数 **/
	APPCONFIG_DoIp_TesterConfigType*                DoIpTesters;                   /** 允许访问的SA地址 **/
	unsigned char                                   DoIpTargetAddresses_num;       /** TA地址个数 **/
	APPCONFIG_DoIp_TargetAddressConfigType* 		DoIpTargetAddresses;           /** TA地址列表 **/
	char                                            broadcast_len;                 /** 广播IP长度,若不设置传0 **/
	char                                            *broadcast_address;            /** 设备上电时广播的IP地址，用于广播指定网段广播xxx.xxx.xxx.255，或全局广播*************** **/
	char                                            multicast_len;                 /** 多播组IP长度,若不设置传0 **/
	char                                            *multicast_address;            /** 加入多播组IP **/
	char                                            ethname_len;                   /** 网卡名称长度 **/
	char                                            *ethname;                      /** 网卡名称 **/

	APPCONFIG_SoAdDoIpTimeConfigType*                SoAdDoIpTime;                  /** 时间参数配置 **/
} APPCONFIG_SoAdDoIP_ConfigType;

typedef enum{
	APPSOADDOIP_DEBUG_NONE = 0x0,
	APPSOADDOIP_DEBUG_ERROR = 0x01,
	APPSOADDOIP_DEBUG_WARN = 0x02,
	APPSOADDOIP_DEBUG_INFO = 0x04,
}APPSOADDOIP_DEBUG_ENUM;

/**********************************************************
 * 函数名称:	gSoadDoipDebugMsg
 * 函数作用:soad中调试信息输出指针
 * 函数参数:	[in] tmp_doipconfig_param:		配置信息
 * 函数返回:	成功返回0，失败返回小于0
**********************************************************/
typedef void (*gappSoadDoipDebugMsg)(APPSOADDOIP_DEBUG_ENUM,const char* format,...);

typedef struct{
	unsigned char                                   debugstatus;                   /** 调试信息的输出状态，APPSOADDOIP_DEBUG_ENUM，可以使用或操作让所有等级的信息均对外输出 **/
	gappSetDgsChnStatus 							SetDgsChnStatus;			   /** 以太网通道占用或者释放请求函数指针 **/
	gappDoipEventNotifier							DoipEventNotifier;			   /** doip层事件上报接口 **/
	gappcallback_handle 							callback_handle;			   /** 诊断数据回调接口 **/
	gappDoIPServerDiagnosticMsg						pDoIPServerDiagnosticMsg;	   /** 数据回调接口,注意该接口应用层无需注册，初始化为NULL **/
	gappSoadDoipDebugMsg                            soaddoip_debugmsg;             /** 调试信息输出的函数指针 **/
	unsigned char                                   routingactivenum;              /** 路由激活的类型个数 **/
	APPCONFIG_DoIp_RoutingActivationConfigType*     routingactivearry;             /** 路由激活的数组 **/
}APPFUNCONFIG_SoAd_ConfigType;

#ifdef __cplusplus
}
#endif

#endif      /* End Of __DOIP_SERVER_APPCONFIG_H__ */
