/*-------------------------------- Arctic Core ------------------------------
 * Copyright (C) 2013, ArcCore AB, Sweden, www.arccore.com.
 * Contact: <<EMAIL>>
 *
 * You may ONLY use this file:
 * 1)if you have a valid commercial ArcCore license and then in accordance with
 * the terms contained in the written license agreement between you and ArcCore,
 * or alternatively
 * 2)if you follow the terms found in GNU General Public License version 2 as
 * published by the Free Software Foundation and appearing in the file
 * LICENSE.GPL included in the packaging of this file or here
 * <http://www.gnu.org/licenses/old-licenses/gpl-2.0.txt>
 *-------------------------------- Arctic Core -----------------------------*/


/*
 * NB! This file is for SOAD internal use only and may only be included from SOAD C-files!
 */

#ifndef SOAD_INTERNAL_H_
#define SOAD_INTERNAL_H_
#include <stdio.h>
#include "soad.h"
//#include "tcpip.h"


#if defined(USE_UDPNM)
#define SOAD_SOCKET_COUNT   4 		// 2 at least,one for doip udp discovery and two for doip tcp,three for udpnm
#else
#define SOAD_SOCKET_COUNT   4 		// 2 at least,one for doip udp discovery and two for doip tcp,three for udpnm
#endif

#define SOAD_PDU_ROUTE_COUNT                    			1  							//not use
#define SOAD_SOCKET_ROUTE_COUNT                 			3 
#define DOIP_ROUTINGACTIVATION_TO_TARGET_RELATION_COUNT  	6
#define DOIP_TESTER_COUNT             						(SOAD_SOCKET_COUNT - 1)  	//one count is udp
#define DOIP_MAX_TESTER_CONNECTIONS       					(DOIP_TESTER_COUNT - 1)  	//connection is less than tester count
#define DOIP_TARGET_COUNT                 					3

#define DOIP_ROUTINGACTIVATION_COUNT  						2	/* 支持的路由激活类型个数 */

#define SOAD_RX_BUFFER_SIZE               	(1024 * 4)			/* DoIP报文最大长度(协议栈4个字节表示长度, 最大支持(4G-1)bytes) */
#define SOAD_RX_DIANOSTIC_MESSAGE_SIZE    	4002				/* 单条UDS诊断消息最大长度(需要与UDS-34h和38h服务回复的保持一致) */
#define MULTICAST_ADDRESS_ONE    			"**********"		/* 组播地址1 */
#define MULTICAST_ADDRESS_TWO    			"**********"		/* 组播地址2 */

#define DOIP_PROTOCOL_VERSION       		0x02				/* DoIP协议版本号，0x03: DoIP 13400-2:2019 */
#define DOIP_PROTOCOL_VERSION_DEFAULT		0xFF				/* DoIP协议版本的默认值0xFF，若收到版本号为该值，DoIP实体应对车辆识别请求报文中的协议版本予以忽略 */

typedef enum _payload_type_
{
	PAYLOAD_TYPE_NACK						= 0x0000,			/* DoIP首部否定响应 */
	PAYLOAD_TYPE_IDREQUEST					= 0x0001,			/* 车辆识别请求 */
	PAYLOAD_TYPE_IDREQUEST_BY_EID			= 0x0002,			/* 带EID的车辆识别请求 */
	PAYLOAD_TYPE_IDREQUEST_BY_VIN			= 0x0003,			/* 带VIN的车辆识别请求 */
	PAYLOAD_TYPE_ANNOUNCEMENT				= 0x0004,			/* 车辆声明/车辆识别响应 */
	PAYLOAD_TYPE_ROUTING_ACTIVATION			= 0x0005,			/* 路由激活请求 */
	PAYLOAD_TYPE_ALIVE_CHECK_REQUEST		= 0x0007,			/* 在线检查请求 */
	PAYLOAD_TYPE_ALIVE_CHECK_RESPONSE		= 0x0008,			/* 在线检查响应 */
	PAYLOAD_TYPE_STATUS_REQUEST				= 0x4001,			/* DoIP实体状态请求 */
	PAYLOAD_TYPE_POWERMOE_REQUEST			= 0x4003,			/* 诊断电源模式请求 */
	PAYLOAD_TYPE_DIAGNOSTIC_MESSAGE			= 0x8001,			/* 诊断报文 */
} PAYLOAD_TYPE_E;

// Generic doip header negative acknowledge codes
typedef enum _doip_nack_
{
	DOIP_E_INCORRECT_PATTERN_FORMAT			= 0x00,				/* 格式错误(关闭套接字) */
	DOIP_E_UNKNOWN_PAYLOAD_TYPE				= 0x01,				/* 未知的负载类型(丢弃DoIP报文) */
	DOIP_E_MESSAGE_TO_LARGE					= 0x02,				/* 报文过长(丢弃DoIP报文) */
	DOIP_E_OUT_OF_MEMORY					= 0x03,				/* 超出内存(丢弃DoIP报文) */
	DOIP_E_INVALID_PAYLOAD_LENGTH			= 0x04,				/* 无效的负载长度(关闭套接字) */
} DOIP_NACK_E;

typedef enum _routing_act_resp_code_
{
	ROUTING_ACTIVATION_UNKNOWN_SA			= 0x00,				/* 未知源地址 */
	ROUTING_ACTIVATION_NO_SOEKET_USE		= 0x01,				/* 当前所有可用套接字已被注册或激活 */
	ROUTING_ACTIVATION_SA_DIFFERENT			= 0x02,				/* 源地址与已激活TCP套接字的逻辑地址不一致 */
	ROUTING_ACTIVATION_DUP_REGISTERED		= 0x03,				/* 源地址在不同的TCP套接字中被注册 */
	ROUTING_ACTIVATION_MISSING_AUT			= 0x04,				/* 缺少认证 */
	ROUTING_ACTIVATION_UNSUPPORT_TYPE		= 0x06,				/* 不支持的路由激活类型 */
	ROUTING_ACTIVATION_SUCCESSFULLY			= 0x10,				/* 路由激活成功 */
	ROUTING_ACTIVATION_CONFIRM				= 0x11,				/* 需要确认才能激活通信 */
} ROUTING_ACT_RESP_CODE_E;

typedef enum {
	TCPIP_IPADDR_STATE_ASSIGNED,
	TCPIP_IPADDR_STATE_ONHOLD,
	TCPIP_IPADDR_STATE_UNASSIGNED,
} TcpIp_IpAddrStateType;

#if  ( SOAD_DEV_ERROR_DETECT == STD_ON )
//#include "Det.h"
#define VALIDATE_RV(_exp,_api,_err,_rv ) \
        if( !(_exp) ) { \
          Det_ReportError(MODULE_ID_SOAD, 0, _api, _err); \
          return _rv; \
        }

#define VALIDATE_NO_RV(_exp,_api,_err ) \
  if( !(_exp) ) { \
          Det_ReportError(MODULE_ID_SOAD, 0, _api, _err); \
          return; \
        }
#define DET_REPORTERROR(_x,_y,_z,_q) Det_ReportError(_x, _y, _z, _q)

#else
#define VALIDATE(_exp,_api,_err )
#define VALIDATE_RV(_exp,_api,_err,_rv )
#define VALIDATE_NO_RV(_exp,_api,_err )
#define DET_REPORTERROR(_x,_y,_z,_q)
#endif

typedef enum {
	SOCKET_UNINIT = 0,
	SOCKET_DUPLICATE,
	SOCKET_INIT,
	SOCKET_TCP_LISTENING,
	SOCKET_TCP_READY,
	SOCKET_UDP_READY
} SocketStateType;

typedef struct {
	uint8								SocketNr;
	SocketStateType						SocketState;
	boolean								SocketProtocolIsTcp;
	const SoAd_SocketConnectionType*	SocketConnectionRef;
	const SoAd_SocketRouteType*			SocketRouteRef;
//	const SoAd_PduRouteType*			PduRouteRef;
	uint32								RemoteIpAddress;
	uint16								RemotePort;
	int									SocketHandle;   		//local socket, tcp or udp server socket, if external test equitment, tcp has no this handle
	int									ConnectionHandle; 		//tcp client socket
	uint32                              TcpinitialInactivityTimer;
} SocketAdminType;

typedef enum {
	PDU_IDLE,
	PDU_IF_SENDING,
	PDU_TP_REQ_BUFFER,
	PDU_TP_SENDING
} PduStatusType;

typedef enum {
	SOAD_ARC_DOIP_IDENTIFICATIONREQUEST_ALL,				/* 车辆识别请求 */
	SOAD_ARC_DOIP_IDENTIFICATIONREQUEST_BY_EID,				/* 带EID的车辆识别请求 */
	SOAD_ARC_DOIP_IDENTIFICATIONREQUEST_BY_VIN,				/* 带VIN的车辆识别请求 */
} SoAd_Arc_DoIp_VehicleIdentificationRequestType;

typedef struct {
	PduStatusType		PduStatus;
} PduAdminListType;

typedef enum {
	DOIP_ARC_CONNECTION_INVALID,							/* 连接未注册 */
	DOIP_ARC_CONNECTION_REGISTERED,							/* 连接已注册 */
} SoAd_ArcDoIpSocketStateType;

typedef struct {
	uint16						sockNr;
	uint16						sa;							/* 诊断仪逻辑地址 */
	uint8						activationType;				/* 路由激活类型 */
	SoAd_ArcDoIpSocketStateType	socketState;				/* socket注册状态 */
	uint32						initialInactivityTimer;
	uint32						generalInactivityTimer;
	uint32						aliveCheckTimer;
	boolean						authenticated;
	boolean						confirmed;
	boolean						awaitingAliveCheckResponse;	/* 等待诊断仪"在线检查响应"标志 */
	uint16						targetIndex;				/* Index of the last/active routing target in the generated DoIp_Cfg_TargetList */
	PduStatusType				pduStatus;					/* Status of last/active pdu routing request */
} DoIp_ArcDoIpSocketStatusType;

typedef enum {
	DOIP_SOCKET_ASSIGNMENT_FAILED,							/* socket分配失败 */
	DOIP_SOCKET_ASSIGNMENT_SUCCESSFUL,						/* socket分配成功 */
	DOIP_SOCKET_ASSIGNMENT_PENDING,							/* socket分配待定 */
} DoIp_Arc_SocketAssignmentResultType;

typedef enum {
	SOAD_ARC_LINKDOWN = 0,									/* We do not currently have a valid IP */
	SOAD_ARC_LINKUP,										/* We have a valid IP to send from */
} SoadArcLinkStatusType;

//extern SocketAdminType gSocketAdminList[SOAD_SOCKET_COUNT];
//extern PduAdminListType gPduAdminList[SOAD_PDU_ROUTE_COUNT];

boolean SoAd_BufferGet(uint32 size, uint8** buffPtr);
void SoAd_BufferFree(uint8* buffPtr);
void SoAd_SocketClose(uint16 sockNr);
void SoAd_SocketStatusCheck(uint16 sockNr, int sockHandle);
uint16 SoAd_SendIpMessage(uint16 sockNr, uint32 msgLen, uint8* buff,uint8 annonce_flag);
SoadArcLinkStatusType SoAd_GetLinkStatus(void);

/* 获取当前建立连接的TCP套接字 */
uint8 SoAd_GetNofCurrentlyUsedTcpSockets(void);

/* 获取所有TCP套接字数量 */
uint8 SoAd_GetNofMaxUsedTcpSockets(void);

void DoIP_HandleTcpRx(uint16 sockNr);
void DoIP_HandleUdpRx(uint16 sockNr);
Std_ReturnType DoIP_HandleTpTransmit(PduIdType SoAdSrcPduId, PduInfoType* SoAdSrcPduInfoPtr);
void DoIP_SendVehicleAnnouncement(uint16 sockNr);
void DoIP_LocalIpAddrAssignmentChg(SoAd_SoConIdType linkId, TcpIp_IpAddrStateType state);
void DoIP_PthreadMutexLock();
void DoIP_PthreadMutexUnlock();
void DoIP_MainFunction(void);

typedef struct {
	uint32		DoIpAliveCheckresponseTimeMs;	/** @req SOAD051_Conf */
	uint32		DoIpControlTimeoutMs;			/** @req SOAD065_Conf */
	uint32		DoIpGenericInactiveTimeMs;		/** @req SOAD052_Conf */
	uint32		DoIpInitialInactiveTimeMs;		/** @req SOAD054_Conf */
	uint32		DoIpResponseTimeoutMs;			/** @req SOAD066_Conf */
	uint32		DoIpVidAnnounceIntervalMs;		/** @req SOAD055_Conf */
	uint32		DoIpVidAnnounceMaxWaitMs;		/** @req SOAD056_Conf */
	uint32		DoIpVidAnnounceMinWaitMs;		/** @req SOAD057_Conf */
	uint32		DoIpVidAnnounceNum;				/** @req SOAD058_Conf */
} USERSoAdDoIpConfigType;	/** @req SOAD050_Conf */

typedef void (*USERDoIp_AuthenticationCallbackType)(void);
typedef void (*USERDoIp_ConfirmationCallbackType)(void);

typedef struct {
	uint8 activationNumber;  //activationType
	USERDoIp_AuthenticationCallbackType authenticationCallback;     /** 该值暂时可以填充0 **/
	USERDoIp_ConfirmationCallbackType confirmationCallback;         /** 该值暂时可以填充0 **/
} USERCONFIG_DoIp_RoutingActivationConfigType;

typedef struct {
	uint8 		tester_count;
	uint8 		target_count;
	uint8		routingactivation_count;
	uint8 		vin_len;
	uint8 *		vin_val;
	uint8 		eid_len;
	uint64 		eid_val;
	uint8 		gid_len;
	uint64 		gid_val;
	uint8 		broadcast_len;
	char_t *	broadcast_address;
	uint8 		multicast_len;
	char_t *	multicast_address;
	uint8		ethname_len;
	char_t *	ethname;

	USERSoAdDoIpConfigType userDoIpConfig;
	uint8                                   routingactivenum;              /** 路由激活的类型个数 **/
	USERCONFIG_DoIp_RoutingActivationConfigType*     routingactivearry;    /** 路由激活的数组 **/
	
} DOIP_CONFIG_PARAM_ST;

typedef enum{
	SOADDOIP_DEBUG_NONE = 0x0,
	SOADDOIP_DEBUG_ERROR = 0x01,
	SOADDOIP_DEBUG_WARN = 0x02,
	SOADDOIP_DEBUG_INFO = 0x04,
}SOADDOIP_DEBUG_ENUM;

/**********************************************************
 * 函数名称:	gSoadDoipDebugMsg
 * 函数作用:soad中调试信息输出指针
 * 函数参数:	[in] tmp_doipconfig_param:		配置信息
 * 函数返回:	成功返回0，失败返回小于0
**********************************************************/
typedef void (*gSoadDoipDebugMsg)(SOADDOIP_DEBUG_ENUM,const char* format,...);

/**********************************************************
 * 函数名称:	gDoipEventNotifier
 * 函数作用:	DoIP事件通知函数指针
 * 函数参数:	[in] event:		事件类型
 * 函数参数:	[in] data:		附加数据
 * 函数返回:	无
**********************************************************/
typedef void(*gDoipEventNotifier)(unsigned char event, void *data);

/**********************************************************
 * 函数名称:	gappSetDgsChnStatus
 * 函数作用:	DoIP设置以太网诊断通断占用或释放状态
 * 函数参数:	[in] status:		0x01:表示以太网通道占用，0x02:表示以太网通道释放
 * 函数返回:	占用或释放成功返回为0，失败返回-1
**********************************************************/
typedef int(*gSetDgsChnStatus)(char status);

/**********************************************************
 * 函数名称:	gDoIPServerDiagnosticMsg
 * 函数作用:	诊断消息接收处理接口
 * 函数参数:	[in] socknr:	套接字句柄
 * 函数参数:	[in] sa:		接收到的sa
 * 函数参数:	[in] ta:		接收到的ta
 * 函数参数:	[in] channel:	接收到消息的通道
 * 函数参数:	[in] recv_buf:	接收到的消息缓冲区
 * 函数参数:	[in] recv_len:	接收到的消息大小
 * 函数返回:	成功返回0, 失败返回-1
**********************************************************/
typedef char (*gDoIPServerDiagnosticMsg)(unsigned short socknr, unsigned short sa, \
		unsigned short ta, unsigned char channel, unsigned char *recv_buf, unsigned short recv_len);

typedef struct {
	unsigned char                               debugstatus;                   /** 调试信息的输出状态，APPSOADDOIP_DEBUG_ENUM，可以使用或操作让所有等级的信息均对外输出 **/
	gSetDgsChnStatus 							SetDgsChnStatus;			   /** 以太网通道占用或者是否状态函数请求函数指针 **/
	gDoipEventNotifier							DoipEventNotifier;			   /** doip层事件上报接口 **/
	gDoIPServerDiagnosticMsg					pDoIPServerDiagnosticMsg;	   /** 数据回调接口,注意该接口应用层无需注册，初始化为NULL **/
	gSoadDoipDebugMsg                           soaddoip_debugmsg;             /** 调试信息输出的函数指针 **/
} DOIP_CONFIG_FUNCALLBACK_ST;

typedef struct{
	char_t 							SoadMulticastAddr[16];	    /** fly add,20220907 **/
	char_t 							SoadBroadcastAddr[16];		/** fly add,20220907 **/
	char_t							EthInterfaceName[16];
	unsigned char debuglevel;
	gSoadDoipDebugMsg soaddoip_dmsg;
	gDoipEventNotifier DoipEventNotifier;
	gSetDgsChnStatus 							SetDgsChnStatus;			   /** 以太网通道占用或者是否状态函数请求函数指针 **/
	SocketAdminType SocketAdminList[SOAD_SOCKET_COUNT];
	PduAdminListType PduAdminList[SOAD_PDU_ROUTE_COUNT];
}soad_extcb_t;

extern soad_extcb_t gsoad_extcb;	

#define DEBUG_SOAD                  /** 定义则调试信息输出 **/
#ifndef DEBUG_SOAD
#define DE_SODOIP_PRINTF(level,format,...)
#else
#define DE_SODOIP_PRINTF(level,format,...)  printf("[%s,%d,level:%s]: " format, __FUNCTION__, __LINE__,level, ##__VA_ARGS__)
#endif

#define SOADDOIP_TRACE_PRINTF(level,fmt,args...) \
	do{  \
		if(level & SOADDOIP_DEBUG_INFO && gsoad_extcb.debuglevel & SOADDOIP_DEBUG_INFO){ \
			if(gsoad_extcb.soaddoip_dmsg != NULL){  \
				gsoad_extcb.soaddoip_dmsg(SOADDOIP_DEBUG_INFO,"[%s,%d]" fmt,__FUNCTION__,__LINE__,##args); \
			}else{ \
				DE_SODOIP_PRINTF("info",fmt,##args); \
			} \
		} \
		else if(level & SOADDOIP_DEBUG_WARN && gsoad_extcb.debuglevel & SOADDOIP_DEBUG_WARN){ \
			if(gsoad_extcb.soaddoip_dmsg != NULL){  \
				gsoad_extcb.soaddoip_dmsg(SOADDOIP_DEBUG_WARN,"[%s,%d]" fmt,__FUNCTION__,__LINE__,##args); \
			}else{ \
				DE_SODOIP_PRINTF("warn",fmt,##args); \
			} \
		} \
		else if(level & SOADDOIP_DEBUG_ERROR && gsoad_extcb.debuglevel & SOADDOIP_DEBUG_ERROR){ \
			if(gsoad_extcb.soaddoip_dmsg != NULL){  \
				gsoad_extcb.soaddoip_dmsg(SOADDOIP_DEBUG_ERROR,"[%s,%d]" fmt,__FUNCTION__,__LINE__,##args); \
			}else{ \
				DE_SODOIP_PRINTF("error",fmt,##args); \
			} \
		} \
	}while(0)

extern int gSoadRecvDebugLevel;		/** add fly,20220903 **/
extern int gSoadSndDebugLevel;		/** add fly,20220903 **/

/**********************************************************
 * 函数名称:	DoIP_ParameterConfig
 * 函数作用:	DoIP参数配置
 * 函数参数:	[in] tmp_doipconfig_param:		配置信息
 * 函数返回:	成功返回0，失败返回小于0
**********************************************************/
int DoIP_ParameterConfig(DOIP_CONFIG_PARAM_ST tmp_doipconfig_param);

/**********************************************************
 * 函数名称:	DoIP_CallbackFUNConfig
 * 函数作用:	DoIP回调函数信息配置
 * 函数参数:	[in] tmp_doipconfig_funcallback:		回调函数信息结构体
 * 函数返回:	成功返回0，失败返回小于0
**********************************************************/
int DoIP_CallbackFUNConfig(DOIP_CONFIG_FUNCALLBACK_ST tmp_doipconfig_funcallback);

#endif			/* SOAD_INTERNAL_H_ */