/********************************************************************************
**
** 文件名:     doip_server.h
** 版权所有:   (c) 2022-2028 厦门雅迅网络股份有限公司
** 文件描述:   doip服务端接口
**
*********************************************************************************
**             修改历史记录
**===============================================================================
**| 日期       | 作者   |  修改记录
**===============================================================================
**| 2022/05/12 | yx |  创建该文件
**
*********************************************************************************/

#include <stddef.h>
#include <string.h>
#include <unistd.h>
#include <stdlib.h>
#include <stdio.h>

#include "soad.h"
#include "doip_server.h"
#include "doip_server_version.h"
#include "doip_server_getfileparameter.h"


/*
********************************************************************************
* define module variable
********************************************************************************
*/


typedef struct {
	gappcallback_handle doipcallback;
	doiprecinfo_st doiprecinfo;
	Config *file_config;
	APPCONFIG_SoAdDoIP_ConfigType *config;
}doipserver_handlest;

static doipserver_handlest sgdoips_hdl = {
	.doipcallback = NULL,
 	.doiprecinfo = {
		.sa = 0x00,
		.ta = 0x00,
		.chn = 0x00,
		.sockNr = 0x00
	},
	.file_config = NULL,
	.config = NULL
};

/**********************************************************
 * 函数名称：DoIP_Server_AppConfig
 * 函数作用：服务端应用层信息配置
 * 函数入参：[in] app_soad_config:配置参数结构体
 * 函数返回值：成功返回0，失败返回-1
**********************************************************/
extern int DoIP_Server_AppConfig(APPCONFIG_SoAdDoIP_ConfigType* app_soad_config);

/**********************************************************
 * 函数名称：DoIP_Server_AppFunConfig
 * 函数作用：服务端应用层回调函数信息配置
 * 函数入参：[in] app_soad_config:配置参数结构体
 * 函数返回值：成功返回0，失败返回-1
**********************************************************/
extern int DoIP_Server_AppFunConfig(APPFUNCONFIG_SoAd_ConfigType* app_soad_funconfig);

/**********************************************************
 * 函数名称：DoIP_Server_AppDeconfig
 * 函数作用：服务端应用层取消配置退出
 * 函数入参：无
 * 函数返回值：成功返回0，失败返回-1
**********************************************************/
extern int DoIP_Server_AppDeconfig(void);

/**********************************************************
 * 函数名称:	DoIP_Server_Diagnostic_msg
 * 函数作用:	诊断消息接收处理接口
 * 函数参数:	[in] socknr:	套接字句柄
 * 函数参数:	[in] sa:		接收到的sa
 * 函数参数:	[in] ta:		接收到的ta
 * 函数参数:	[in] channel:	接收到消息的通道
 * 函数参数:	[in] recv_buf:	接收到的消息缓冲区
 * 函数参数:	[in] recv_len:	接收到的消息大小
 * 函数返回:	成功返回0, 失败返回-1
**********************************************************/
static char DoIP_Server_Diagnostic_msg(unsigned short socknr, unsigned short sa, \
		unsigned short ta, unsigned char channel, unsigned char *recv_buf, unsigned short recv_len)
{
	char res = -1;

	if (sgdoips_hdl.doipcallback != NULL)
	{
		res = 0;
		sgdoips_hdl.doiprecinfo.sockNr = socknr;
		sgdoips_hdl.doiprecinfo.sa = sa;
		sgdoips_hdl.doiprecinfo.ta = ta;
		sgdoips_hdl.doiprecinfo.chn = channel;
		sgdoips_hdl.doipcallback(sgdoips_hdl.doiprecinfo, recv_buf, recv_len);
	}
	
	return res;
}

/**********************************************************
 * 函数名称:	DoIP_Server_Diagnostic_Send_Msg
 * 函数作用:	诊断消息发送处理接口
 * 函数参数:	[in] sockNr:句柄，填充接收到数据的相关句柄信息
 * 函数参数:	[in] snd_buf:		发送的数据缓冲区
 * 函数参数:	[in] snd_len:		待发送的数据长度
 * 函数返回:	成功返回0, 失败返回-1
**********************************************************/
int DoIP_Server_Diagnostic_Send_Msg(unsigned short sockNr, unsigned char *snd_buf, unsigned short snd_len)
{
	int ret = -1;
	ret = SoAdTp_Transmit_Dianostic(sockNr, snd_buf, snd_len);
	return ret;
}

/**********************************************************
 * 函数名称:	DoIP_Server_Init
 * 函数作用:	doip服务接口初始化
 * 函数参数:	[in] pAppfunSoadConfig:回调信息相关配置
 * 函数参数:	[in] pAppSoadConfig: app相关配置信息,若第三个参数为NULL,则该参数必须输入值否则初始化失败。
 * 函数参数:	[in] parampath: json相关配置文件的存储路径
 * 函数返回:	成功返回0, 失败返回-1
 * 注意：输入的参数需具备Init到DeInit的全生命周期
**********************************************************/
int DoIP_Server_Init(APPFUNCONFIG_SoAd_ConfigType *pAppfunSoadConfig,APPCONFIG_SoAdDoIP_ConfigType *pAppSoadConfig,char* parampath)
{
	int ret = -1, index = 0;

	if(pAppfunSoadConfig){
		sgdoips_hdl.doipcallback = pAppfunSoadConfig->callback_handle;
		pAppfunSoadConfig->pDoIPServerDiagnosticMsg = DoIP_Server_Diagnostic_msg;
		DoIP_Server_AppFunConfig(pAppfunSoadConfig);
	}

	/* 若初始传入的参数值不变, 则该接口可以只初始化一次 */
	if (pAppSoadConfig)
	{
		ret = DoIP_Server_AppConfig(pAppSoadConfig);
		if(ret == -1){
			return -1;
		}
	} else {
		if(parampath == NULL || (access(parampath,F_OK) != 0)){
			return -2;
		}
		sgdoips_hdl.file_config = parse_config_from_file(parampath);
		if(sgdoips_hdl.file_config != NULL){
			if(sgdoips_hdl.config == NULL){
				sgdoips_hdl.config = (APPCONFIG_SoAdDoIP_ConfigType *)calloc(1,sizeof(APPCONFIG_SoAdDoIP_ConfigType));
				sgdoips_hdl.config->SoAdDoIpTime = (APPCONFIG_SoAdDoIpTimeConfigType*)calloc(1,sizeof(APPCONFIG_SoAdDoIpTimeConfigType));
				sgdoips_hdl.config->DoIpTargetAddresses = (APPCONFIG_DoIp_TargetAddressConfigType*)calloc(1,sizeof(APPCONFIG_DoIp_TargetAddressConfigType));
				sgdoips_hdl.config->DoIpTesters = (APPCONFIG_DoIp_TesterConfigType*)calloc(1,sizeof(APPCONFIG_DoIp_TesterConfigType));
					
				sgdoips_hdl.config->broadcast_address = sgdoips_hdl.file_config->broadcastaddr;
				sgdoips_hdl.config->broadcast_len = strlen(sgdoips_hdl.file_config->broadcastaddr);
				sgdoips_hdl.config->multicast_address = sgdoips_hdl.file_config->multicastaddr;
				sgdoips_hdl.config->multicast_len = strlen(sgdoips_hdl.file_config->multicastaddr);
				
				sgdoips_hdl.config->DoIpTargetAddresses_num = sgdoips_hdl.file_config->DoipTaCount;
				for(index = 0 ; index < sgdoips_hdl.config->DoIpTargetAddresses_num; index++){
					sgdoips_hdl.config->DoIpTargetAddresses[index].addressValue = sgdoips_hdl.file_config->DoipTaList[index].addressValue;
					sgdoips_hdl.config->DoIpTargetAddresses[index].rxPdu = sgdoips_hdl.file_config->DoipTaList[index].rxPdu;
					sgdoips_hdl.config->DoIpTargetAddresses[index].txPdu = sgdoips_hdl.file_config->DoipTaList[index].txPdu;
				}
				
				sgdoips_hdl.config->DoIpTesters_num = sgdoips_hdl.file_config->DoipTesterSaCount;
				for(index = 0 ; index < sgdoips_hdl.config->DoIpTesters_num; index++){
					sgdoips_hdl.config->DoIpTesters[index].address = sgdoips_hdl.file_config->DoipTesterSaList[index].address;
					sgdoips_hdl.config->DoIpTesters[index].numBytes = sgdoips_hdl.file_config->DoipTesterSaList[index].numbytes;
				}
				
				sgdoips_hdl.config->eid_val = sgdoips_hdl.file_config->EID;
				sgdoips_hdl.config->eid_len = 6;
				sgdoips_hdl.config->gid_val = sgdoips_hdl.file_config->GID;
				sgdoips_hdl.config->gid_len = 6;
				sgdoips_hdl.config->vin_val = sgdoips_hdl.file_config->VIN;
				sgdoips_hdl.config->vin_len = strlen(sgdoips_hdl.file_config->VIN);
				sgdoips_hdl.config->ethname = sgdoips_hdl.file_config->ethname;
				sgdoips_hdl.config->ethname_len = strlen(sgdoips_hdl.file_config->ethname);
				
				sgdoips_hdl.config->SoAdDoIpTime->DoIpAliveCheckresponseTimeMs = sgdoips_hdl.file_config->AliveCheckresponseTimeMs;
				sgdoips_hdl.config->SoAdDoIpTime->DoIpControlTimeoutMs = sgdoips_hdl.file_config->DoIpControlTimeoutMs;
				sgdoips_hdl.config->SoAdDoIpTime->DoIpGenericInactiveTimeMs = sgdoips_hdl.file_config->GenericInactiveTimeMs;
				sgdoips_hdl.config->SoAdDoIpTime->DoIpInitialInactiveTimeMs = sgdoips_hdl.file_config->InitialInactiveTimeMs;
				sgdoips_hdl.config->SoAdDoIpTime->DoIpResponseTimeoutMs = sgdoips_hdl.file_config->ResponseTimeoutMs;
				sgdoips_hdl.config->SoAdDoIpTime->DoIpVidAnnounceIntervalMs = sgdoips_hdl.file_config->VidAnnounceIntervalMs;
				sgdoips_hdl.config->SoAdDoIpTime->DoIpVidAnnounceMaxWaitMs = sgdoips_hdl.file_config->VidAnnounceMaxWaitMs;
				sgdoips_hdl.config->SoAdDoIpTime->DoIpVidAnnounceMinWaitMs = sgdoips_hdl.file_config->VidAnnounceMinWaitMs;
				sgdoips_hdl.config->SoAdDoIpTime->DoIpVidAnnounceNum = sgdoips_hdl.file_config->VidAnnounceNum;

				#if 1
				printf("%s,%d,eid = %lld,gid = %lld,vin = %s\n",__func__,__LINE__,sgdoips_hdl.config->eid_val,sgdoips_hdl.config->gid_val,sgdoips_hdl.config->vin_val);
				printf("%s,%d,ethname_len = %d,ethname = %s\n",__func__,__LINE__,sgdoips_hdl.config->ethname_len,sgdoips_hdl.file_config->ethname);
				for(index = 0 ; index < sgdoips_hdl.config->DoIpTesters_num; index++){
					printf("%s,%d,sa = %d,numBytes = %d\n",__func__,__LINE__,sgdoips_hdl.config->DoIpTesters[index].address,sgdoips_hdl.config->DoIpTesters[index].numBytes);
				}
				for(index = 0 ; index < sgdoips_hdl.config->DoIpTargetAddresses_num; index++){
					printf("%s,%d,ta = %d,numBytes = %d\n",__func__,__LINE__,sgdoips_hdl.config->DoIpTargetAddresses[index].addressValue,sgdoips_hdl.config->DoIpTargetAddresses[index].rxPdu);
				}
				printf("%s,%d,ethname_len = %d,broadcast_address = %s\n",__func__,__LINE__,sgdoips_hdl.config->broadcast_len,sgdoips_hdl.config->broadcast_address);
				printf("%s,%d,multicast_len = %d,multicast_address = %s\n",__func__,__LINE__,sgdoips_hdl.config->multicast_len,sgdoips_hdl.config->multicast_address);
			
				printf("%s,%d,DoIpAliveCheckresponseTimeMs = %d,DoIpControlTimeoutMs = %d\n",__func__,__LINE__,sgdoips_hdl.config->SoAdDoIpTime->DoIpAliveCheckresponseTimeMs,sgdoips_hdl.config->SoAdDoIpTime->DoIpControlTimeoutMs);
				printf("%s,%d,DoIpGenericInactiveTimeMs = %d,DoIpInitialInactiveTimeMs = %d\n",__func__,__LINE__,sgdoips_hdl.config->SoAdDoIpTime->DoIpGenericInactiveTimeMs,sgdoips_hdl.config->SoAdDoIpTime->DoIpInitialInactiveTimeMs);
				printf("%s,%d,DoIpResponseTimeoutMs = %d,DoIpVidAnnounceIntervalMs = %d\n",__func__,__LINE__,sgdoips_hdl.config->SoAdDoIpTime->DoIpResponseTimeoutMs,sgdoips_hdl.config->SoAdDoIpTime->DoIpVidAnnounceIntervalMs);
				printf("%s,%d,DoIpVidAnnounceMaxWaitMs = %d,DoIpVidAnnounceMinWaitMs = %d\n",__func__,__LINE__,sgdoips_hdl.config->SoAdDoIpTime->DoIpVidAnnounceMaxWaitMs,sgdoips_hdl.config->SoAdDoIpTime->DoIpVidAnnounceMinWaitMs);
				printf("%s,%d,DoIpVidAnnounceNum = %d\n",__func__,__LINE__,sgdoips_hdl.config->SoAdDoIpTime->DoIpVidAnnounceNum);
				#endif
			}else{
				return -3;
			}
		}else{
			return -4;
		}
		ret = DoIP_Server_AppConfig(sgdoips_hdl.config);
		if(ret == -1){
			return -1;
		}
	}
	
	return SoAd_Doip_Init();
}

/**********************************************************
 * 函数名称:	DoIP_Server_DeInit
 * 函数作用:	doip服务接口去初始化
 * 函数参数:	无
 * 函数返回:	成功返回0, 失败返回小于0
**********************************************************/
int DoIP_Server_DeInit(void)
{
	int ret = -1;
	ret = SoAd_Doip_DeInit();

	DoIP_Server_AppDeconfig();

	if(sgdoips_hdl.file_config){
		free(sgdoips_hdl.config->SoAdDoIpTime);
		free(sgdoips_hdl.config);
	}
	
	if(sgdoips_hdl.file_config){
		free(sgdoips_hdl.file_config);
	}
	return ret;
}

/**********************************************************
 * 函数名称:	DoIP_Server_Version
 * 函数作用:	doip服务接口去初始化
 * 函数参数:	 [in/out] verbuf ：返回版本号的内容，外部需提供至少20字节的栈空间
 *           [in/out] verbflen：实际空间字节数/返回版本号的实际长度
 * 函数返回:	成功返回0, 失败返回小于0
**********************************************************/
int DoIP_Server_Version(char *verbuf,int *verlen)
{
	if(verbuf != NULL){
		memset(verbuf,0,*verlen);
		*verlen = strlen(DOIPS_VERSION);
		memcpy(verbuf,DOIPS_VERSION,*verlen);
			return 0;
	}else{
		return -1;
	}
}

