/********************************************************************************
**
** 文件名:     doip_server.h
** 版权所有:   (c) 2022-2028 厦门雅迅网络股份有限公司
** 文件描述:   doip服务端接口
**
*********************************************************************************
**             修改历史记录
**===============================================================================
**| 日期       | 作者   |  修改记录
**===============================================================================
**| 2022/05/12 | yx |  创建该文件
**
*********************************************************************************/
#ifndef __DOIP_SERVER_H__
#define __DOIP_SERVER_H__

#ifdef __cplusplus
extern "C"
{
#endif

#include "doip_server_appconfig.h"

/**********************************************************
 * 函数名称:	DoIP_Server_Diagnostic_Send_Msg
 * 函数作用:	诊断消息发送处理接口
 * 函数参数:	[in] sockNr:句柄，填充接收到数据的相关句柄信息
 * 函数参数:	[in] snd_buf:		发送的数据缓冲区
 * 函数参数:	[in] snd_len:		待发送的数据长度
 * 函数返回:	成功返回0, 失败返回-1
**********************************************************/
int DoIP_Server_Diagnostic_Send_Msg(unsigned short sockNr, unsigned char *snd_buf, unsigned short snd_len);

/**********************************************************
 * 函数名称:	DoIP_Server_Init
 * 函数作用:	doip服务接口初始化
 * 函数参数:	[in] pAppfunSoadConfig:回调信息相关配置
 * 函数参数:	[in/out] pAppSoadConfig: app相关配置信息,若第三个参数为NULL,则该参数必须输入值否则初始化失败。
 * 函数参数:	[in] parampath: app相关配置文件的存储路径
 * 函数返回:	成功返回0, 失败返回-1
**********************************************************/
int DoIP_Server_Init(APPFUNCONFIG_SoAd_ConfigType *pAppfunSoadConfig,APPCONFIG_SoAdDoIP_ConfigType *pAppSoadConfig,char* parampath);

/**********************************************************
 * 函数名称:	DoIP_Server_DeInit
 * 函数作用:	doip服务接口去初始化
 * 函数参数:	无
 * 函数返回:	成功返回0, 失败返回小于0
**********************************************************/
int DoIP_Server_DeInit(void);

/**********************************************************
 * 函数名称:	DoIP_Server_Version
 * 函数作用:	doip服务接口去初始化
 * 函数参数:	 [in/out] verbuf ：返回版本号的内容，外部需提供至少20字节的栈空间
 *			 [in/out] verbflen：实际空间字节数/返回版本号的实际长度
 * 函数返回:	成功返回0, 失败返回小于0
**********************************************************/
int DoIP_Server_Version(char *verbuf,int *verlen);

#ifdef __cplusplus
}
#endif

#endif      /* End Of __DOIP_SERVER_H__ */
