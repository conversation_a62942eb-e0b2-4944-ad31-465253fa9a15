
#include path
INCLUDES    += -I../h

#INSTALL_SRC_INC:=./h/*
#INSTALL_DES_INC:=$(SDK_EVN_ENTRY)/env/include/doipc/

#INSTALL_SRC_AR:=./_build/*
#INSTALL_DEC_AR:= $(SDK_EVN_ENTRY)/env/lib/usrlib

# source files, sub directory will then add to this, all source files must use ".c" as the extension name        
SOURCES += c/test_main.c

# sub directories, sub directory will then add to this
SUBDIRS		+= c

