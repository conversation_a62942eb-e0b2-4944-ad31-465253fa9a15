# DoIP (Diagnostics over Internet Protocol) 设计方案

## 1. 项目概述

### 1.1 项目简介
本项目实现了基于ISO 13400标准的DoIP (Diagnostics over Internet Protocol) 服务端，支持通过以太网进行车辆诊断通信。系统采用分层架构设计，提供完整的DoIP协议栈实现。

### 1.2 版本信息
- **版本号**: V1.0.2
- **协议版本**: DoIP 0x02 (支持ISO 13400-2:2019)
- **开发语言**: C语言
- **目标平台**: Linux (支持多种网络接口)

### 1.3 主要特性
- 支持UDP和TCP双协议栈
- 车辆识别与声明机制
- 路由激活与管理
- 诊断消息传输
- 在线检查与超时管理
- 多播/广播支持
- 灵活的配置管理

## 2. 系统架构

### 2.1 分层架构设计

DoIP系统采用五层架构设计，从上到下分别是应用层、DoIP协议层、SoAd层、传输层和网络层。每层都有明确的职责分工和接口定义。

```mermaid
graph TB
    subgraph "应用层 (Application Layer)"
        A1[诊断服务<br/>Diagnostic Services]
        A2[配置管理<br/>Configuration Management]
        A3[事件通知<br/>Event Notification]
    end

    subgraph "DoIP协议层 (DoIP Protocol Layer)"
        B1[消息处理<br/>Message Handler]
        B2[路由管理<br/>Routing Management]
        B3[状态管理<br/>State Management]
        B4[车辆识别<br/>Vehicle Identification]
        B5[在线检查<br/>Alive Check]
    end

    subgraph "SoAd层 (Socket Adapter Layer)"
        C1[套接字管理<br/>Socket Administration]
        C2[数据传输<br/>Data Transfer]
        C3[连接管理<br/>Connection Management]
        C4[缓冲区管理<br/>Buffer Management]
    end

    subgraph "传输层 (Transport Layer)"
        D1[TCP协议<br/>TCP Protocol<br/>Port 13400]
        D2[UDP协议<br/>UDP Protocol<br/>Port 13400]
        D3[Socket API<br/>Linux Socket]
    end

    subgraph "网络层 (Network Layer)"
        E1[以太网接口<br/>Ethernet Interface]
        E2[IP协议栈<br/>IP Stack]
    end

    %% 应用层内部连接
    A1 -.-> A2
    A2 -.-> A3

    %% DoIP协议层内部连接
    B1 --> B2
    B2 --> B3
    B1 --> B4
    B1 --> B5

    %% SoAd层内部连接
    C1 --> C2
    C1 --> C3
    C2 --> C4

    %% 传输层内部连接
    D1 -.-> D3
    D2 -.-> D3

    %% 层间连接
    A1 --> B1
    A2 --> B2
    A3 --> B3

    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C1
    B5 --> C1

    C1 --> D1
    C1 --> D2
    C2 --> D1
    C2 --> D2
    C3 --> D1

    D1 --> E1
    D2 --> E1
    D3 --> E2
    E2 --> E1

    %% 样式定义
    classDef appLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef doipLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef soadLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef transLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef netLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    class A1,A2,A3 appLayer
    class B1,B2,B3,B4,B5 doipLayer
    class C1,C2,C3,C4 soadLayer
    class D1,D2,D3 transLayer
    class E1,E2 netLayer
```

### 2.2 模块关系图

DoIP系统由多个模块组成，各模块之间有清晰的依赖关系和接口定义：

```mermaid
graph TB
    subgraph "应用层模块"
        APP[doip_server.c<br/>主服务接口]
        CONFIG[doip_server_appconfig.c<br/>应用配置管理]
        PARAM[doip_server_getfileparameter.c<br/>JSON配置解析]
    end

    subgraph "DoIP协议层模块"
        DOIP[soad_doip.c<br/>DoIP协议处理核心]
        CFG[doip_cfg.c<br/>静态配置定义]
    end

    subgraph "SoAd适配层模块"
        SOAD[soad.c<br/>套接字适配器]
    end

    subgraph "头文件模块"
        H1[doip_server.h<br/>主接口定义]
        H2[doip_server_appconfig.h<br/>应用配置结构]
        H3[doip_server_getfileparameter.h<br/>JSON解析接口]
        H4[soad.h<br/>SoAd接口定义]
        H5[soad_internal.h<br/>内部结构定义]
        H6[soad_types.h<br/>类型定义]
        H7[soad_configtypes.h<br/>配置类型定义]
    end

    subgraph "第三方库"
        CJSON[cjson.c/cjson.h<br/>JSON解析库]
        SOCKET[Linux Socket API<br/>网络通信]
        PTHREAD[pthread<br/>线程管理]
    end

    subgraph "测试模块"
        TEST[test_main.c<br/>测试程序]
    end

    %% 应用层依赖关系
    APP --> CONFIG
    APP --> PARAM
    APP --> H1
    APP --> H2
    CONFIG --> H2
    PARAM --> H3
    PARAM --> CJSON

    %% 协议层依赖关系
    APP --> DOIP
    DOIP --> CFG
    DOIP --> H4
    DOIP --> H5
    CFG --> H5
    CFG --> H6
    CFG --> H7

    %% SoAd层依赖关系
    DOIP --> SOAD
    SOAD --> H4
    SOAD --> H5
    SOAD --> H6
    SOAD --> SOCKET
    SOAD --> PTHREAD

    %% 头文件依赖关系
    H1 --> H2
    H2 --> H6
    H4 --> H5
    H4 --> H6
    H5 --> H6
    H5 --> H7
    H7 --> H6

    %% 测试模块依赖
    TEST --> APP
    TEST --> H1
    TEST --> H2

    %% 样式定义
    classDef appModule fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef protocolModule fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef soadModule fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef headerModule fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef thirdParty fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef testModule fill:#f1f8e9,stroke:#689f38,stroke-width:2px

    class APP,CONFIG,PARAM appModule
    class DOIP,CFG protocolModule
    class SOAD soadModule
    class H1,H2,H3,H4,H5,H6,H7 headerModule
    class CJSON,SOCKET,PTHREAD thirdParty
    class TEST testModule
```

### 2.3 核心模块组成

#### 2.3.1 DoIP服务器模块 (doip_server)
- **文件**: `c/doip_server.c`, `h/doip_server.h`
- **功能**: 提供DoIP服务的主要接口
- **主要接口**:
  - `DoIP_Server_Init()`: 服务初始化
  - `DoIP_Server_DeInit()`: 服务去初始化
  - `DoIP_Server_Diagnostic_msg()`: 诊断消息处理

#### 2.3.2 SoAd套接字适配层 (soad)
- **文件**: `src_c/soad.c`, `src_h/soad.h`
- **功能**: 套接字管理和网络通信抽象
- **主要功能**:
  - 套接字创建、绑定、监听
  - TCP/UDP消息收发
  - 连接状态管理
  - 网络事件处理

#### 2.3.3 DoIP协议处理模块 (soad_doip)
- **文件**: `src_c/soad_doip.c`
- **功能**: DoIP协议消息处理核心
- **主要功能**:
  - DoIP消息解析与构造
  - 车辆识别处理
  - 路由激活管理
  - 诊断消息路由
  - 在线检查机制

#### 2.3.4 配置管理模块
- **文件**: `src_c/doip_cfg.c`, `c/doip_server_appconfig.c`
- **功能**: 系统配置管理
- **配置内容**:
  - 套接字配置
  - DoIP参数配置
  - 超时参数配置
  - 地址映射配置

## 3. 核心数据结构

### 3.1 套接字连接配置
```c
typedef struct {
    uint16 SocketId;                    // 套接字ID
    char* SocketLocalIpAddress;         // 本地IP地址
    uint16 SocketLocalPort;             // 本地端口 (13400)
    char* SocketRemoteIpAddress;        // 远程IP地址
    uint16 SocketRemotePort;            // 远程端口
    SoAd_SocketProtocolType SocketProtocol; // TCP/UDP协议类型
    boolean SocketTcpInitiate;          // TCP主动连接标志
    boolean SocketTcpNoDelay;           // TCP无延迟标志
    boolean SocketUdpListenOnly;        // UDP仅监听标志
    SoAd_AutosarConnectorType AutosarConnectorType; // 连接器类型
    boolean PduHeaderEnable;            // PDU头使能
    boolean SocketAutosarApi;           // AUTOSAR API使能
    boolean ResourceManagementEnable;   // 资源管理使能
    boolean PduProvideBufferEnable;     // PDU缓冲区提供使能
} SoAd_SocketConnectionType;
```

### 3.2 DoIP配置结构
```c
typedef struct {
    uint32 DoIpAliveCheckresponseTimeMs;    // 在线检查响应超时 (500ms)
    uint32 DoIpControlTimeoutMs;            // 控制超时 (2000ms)
    uint32 DoIpGenericInactiveTimeMs;       // 通用非活动超时 (5分钟)
    uint8* DoIpHostNameOpt;                 // 主机名选项
    uint32 DoIpInitialInactiveTimeMs;       // 初始非活动超时 (2000ms)
    uint32 DoIpResponseTimeoutMs;           // 响应超时 (500ms)
    uint32 DoIpVidAnnounceIntervalMs;       // 车辆识别声明间隔 (500ms)
    uint32 DoIpVidAnnounceMaxWaitMs;        // 车辆识别声明最大等待 (1500ms)
    uint32 DoIpVidAnnounceMinWaitMs;        // 车辆识别声明最小等待 (0ms)
    uint32 DoIpVidAnnounceNum;              // 车辆识别声明次数 (3次)
    SoAd_CallbackDoIpPowerModeFncType DoipPowerModeCallback; // 电源模式回调
} SoAd_DoIpConfigType;
```

### 3.3 套接字状态管理
```c
typedef struct {
    uint8 SocketNr;                         // 套接字编号
    SocketStateType SocketState;            // 套接字状态
    boolean SocketProtocolIsTcp;            // 是否TCP协议
    const SoAd_SocketConnectionType* SocketConnectionRef; // 连接配置引用
    const SoAd_SocketRouteType* SocketRouteRef;          // 路由配置引用
    uint32 RemoteIpAddress;                 // 远程IP地址
    uint16 RemotePort;                      // 远程端口
    int SocketHandle;                       // 套接字句柄
    int ConnectionHandle;                   // 连接句柄
    uint32 TcpinitialInactivityTimer;       // TCP初始非活动定时器
} SocketAdminType;
```

### 3.4 DoIP连接状态
```c
typedef struct {
    uint16 sockNr;                          // 套接字编号
    uint16 sa;                              // 诊断仪逻辑地址
    uint8 activationType;                   // 路由激活类型
    SoAd_ArcDoIpSocketStateType socketState; // 套接字注册状态
    uint32 initialInactivityTimer;          // 初始非活动定时器
    uint32 generalInactivityTimer;          // 通用非活动定时器
    uint32 aliveCheckTimer;                 // 在线检查定时器
    boolean authenticated;                  // 认证状态
    boolean confirmed;                      // 确认状态
    boolean awaitingAliveCheckResponse;     // 等待在线检查响应标志
    uint16 targetIndex;                     // 目标索引
    PduStatusType pduStatus;                // PDU状态
} DoIp_ArcDoIpSocketStatusType;
```

## 4. DoIP协议消息类型

### 4.1 消息类型定义
```c
typedef enum {
    PAYLOAD_TYPE_NACK = 0x0000,                 // DoIP首部否定响应
    PAYLOAD_TYPE_IDREQUEST = 0x0001,            // 车辆识别请求
    PAYLOAD_TYPE_IDREQUEST_BY_EID = 0x0002,     // 带EID的车辆识别请求
    PAYLOAD_TYPE_IDREQUEST_BY_VIN = 0x0003,     // 带VIN的车辆识别请求
    PAYLOAD_TYPE_ANNOUNCEMENT = 0x0004,          // 车辆声明/车辆识别响应
    PAYLOAD_TYPE_ROUTING_ACTIVATION = 0x0005,    // 路由激活请求
    PAYLOAD_TYPE_ALIVE_CHECK_REQUEST = 0x0007,   // 在线检查请求
    PAYLOAD_TYPE_ALIVE_CHECK_RESPONSE = 0x0008,  // 在线检查响应
    PAYLOAD_TYPE_STATUS_REQUEST = 0x4001,        // DoIP实体状态请求
    PAYLOAD_TYPE_POWERMOE_REQUEST = 0x4003,      // 诊断电源模式请求
    PAYLOAD_TYPE_DIAGNOSTIC_MESSAGE = 0x8001,    // 诊断报文
} PAYLOAD_TYPE_E;
```

### 4.2 DoIP消息格式
```
DoIP Header (8 bytes):
┌─────────────┬─────────────┬─────────────┬─────────────┐
│ Protocol    │ Inverse     │ Payload     │ Payload     │
│ Version     │ Protocol    │ Type        │ Length      │
│ (1 byte)    │ Version     │ (2 bytes)   │ (4 bytes)   │
│             │ (1 byte)    │             │             │
└─────────────┴─────────────┴─────────────┴─────────────┘

Payload Data (Variable length):
┌─────────────────────────────────────────────────────────┐
│                    Payload Data                         │
│                 (Length bytes)                          │
└─────────────────────────────────────────────────────────┘
```

## 5. 主要功能流程

### 5.1 DoIP消息处理流程

DoIP系统根据接收到的网络消息类型（UDP/TCP）和DoIP消息类型进行不同的处理流程：

```mermaid
graph TD
    Start([网络消息接收]) --> Protocol{协议类型}

    Protocol -->|UDP| UDP_Handler[UDP消息处理<br/>DoIP_HandleUdpRx]
    Protocol -->|TCP| TCP_Handler[TCP消息处理<br/>DoIP_HandleTcpRx]

    UDP_Handler --> UDP_Parse[解析DoIP头部]
    TCP_Handler --> TCP_Parse[解析DoIP头部]

    UDP_Parse --> UDP_Validate{验证协议版本}
    TCP_Parse --> TCP_Validate{验证协议版本}

    UDP_Validate -->|有效| UDP_Type{消息类型}
    TCP_Validate -->|有效| TCP_Type{消息类型}
    UDP_Validate -->|无效| UDP_Discard[丢弃消息]
    TCP_Validate -->|无效| TCP_Discard[丢弃消息]

    UDP_Type -->|0x0001| VID_REQ[车辆识别请求<br/>handleVehicleIdentificationReq]
    UDP_Type -->|0x0002| VID_EID[带EID车辆识别请求]
    UDP_Type -->|0x0003| VID_VIN[带VIN车辆识别请求]
    UDP_Type -->|0x4001| STATUS_REQ[实体状态请求<br/>handleEntityStatusReq]
    UDP_Type -->|0x4003| POWER_REQ[电源模式请求<br/>handlePowerModeCheckReq]

    TCP_Type -->|0x0005| ROUTE_ACT[路由激活请求<br/>handleRoutingActivationReq]
    TCP_Type -->|0x0007| ALIVE_REQ[在线检查请求<br/>handleAliveCheckReq]
    TCP_Type -->|0x0008| ALIVE_RESP[在线检查响应<br/>handleAliveCheckResp]
    TCP_Type -->|0x8001| DIAG_MSG[诊断消息<br/>handleDiagnosticMessage]

    VID_REQ --> VID_Process[构造车辆识别响应]
    VID_EID --> VID_Process
    VID_VIN --> VID_Process
    VID_Process --> VID_Send[UDP广播发送响应<br/>PAYLOAD_TYPE_ANNOUNCEMENT]

    STATUS_REQ --> STATUS_Process[构造状态响应]
    STATUS_Process --> STATUS_Send[UDP发送状态响应]

    POWER_REQ --> POWER_Process[检查电源模式]
    POWER_Process --> POWER_Send[UDP发送电源模式响应]

    ROUTE_ACT --> ROUTE_Validate{验证SA地址}
    ROUTE_Validate -->|有效| ROUTE_Auth{需要认证?}
    ROUTE_Validate -->|无效| ROUTE_NACK[发送NACK响应]
    ROUTE_Auth -->|是| AUTH_Check[执行认证检查]
    ROUTE_Auth -->|否| ROUTE_Confirm{需要确认?}
    AUTH_Check --> AUTH_Result{认证结果}
    AUTH_Result -->|成功| ROUTE_Confirm
    AUTH_Result -->|失败| ROUTE_NACK
    ROUTE_Confirm -->|是| CONFIRM_Check[执行确认检查]
    ROUTE_Confirm -->|否| ROUTE_Success[路由激活成功]
    CONFIRM_Check --> CONFIRM_Result{确认结果}
    CONFIRM_Result -->|成功| ROUTE_Success
    CONFIRM_Result -->|失败| ROUTE_NACK
    ROUTE_Success --> ROUTE_ACK[发送ACK响应<br/>启动定时器]

    ALIVE_REQ --> ALIVE_Process[处理在线检查]
    ALIVE_Process --> ALIVE_ACK[发送在线检查响应]

    ALIVE_RESP --> ALIVE_Validate{验证SA地址}
    ALIVE_Validate -->|有效| ALIVE_Reset[重置非活动定时器]
    ALIVE_Validate -->|无效| ALIVE_Ignore[忽略响应]

    DIAG_MSG --> DIAG_Parse[解析SA/TA地址]
    DIAG_Parse --> DIAG_Validate{验证地址映射}
    DIAG_Validate -->|有效| DIAG_Callback[调用应用层回调]
    DIAG_Validate -->|无效| DIAG_NACK[发送NACK响应]
    DIAG_Callback --> DIAG_Response{需要响应?}
    DIAG_Response -->|是| DIAG_Send[发送诊断响应]
    DIAG_Response -->|否| DIAG_End[处理完成]

    VID_Send --> End([处理完成])
    STATUS_Send --> End
    POWER_Send --> End
    ROUTE_ACK --> End
    ROUTE_NACK --> End
    ALIVE_ACK --> End
    ALIVE_Reset --> End
    ALIVE_Ignore --> End
    DIAG_Send --> End
    DIAG_NACK --> End
    DIAG_End --> End
    UDP_Discard --> End
    TCP_Discard --> End

    %% 样式定义
    classDef startEnd fill:#4caf50,stroke:#2e7d32,stroke-width:2px,color:#fff
    classDef decision fill:#ff9800,stroke:#f57c00,stroke-width:2px,color:#fff
    classDef process fill:#2196f3,stroke:#1976d2,stroke-width:2px,color:#fff
    classDef handler fill:#9c27b0,stroke:#7b1fa2,stroke-width:2px,color:#fff
    classDef error fill:#f44336,stroke:#d32f2f,stroke-width:2px,color:#fff

    class Start,End startEnd
    class Protocol,UDP_Validate,TCP_Validate,UDP_Type,TCP_Type,ROUTE_Validate,ROUTE_Auth,AUTH_Result,ROUTE_Confirm,CONFIRM_Result,ALIVE_Validate,DIAG_Validate,DIAG_Response decision
    class UDP_Parse,TCP_Parse,VID_Process,STATUS_Process,POWER_Process,AUTH_Check,CONFIRM_Check,ALIVE_Process,DIAG_Parse,DIAG_Callback process
    class UDP_Handler,TCP_Handler,VID_REQ,VID_EID,VID_VIN,STATUS_REQ,POWER_REQ,ROUTE_ACT,ALIVE_REQ,ALIVE_RESP,DIAG_MSG handler
    class UDP_Discard,TCP_Discard,ROUTE_NACK,ALIVE_Ignore,DIAG_NACK error
```

### 5.2 系统初始化流程
```
1. DoIP_Server_Init()
   ├── 解析配置参数 (JSON文件或结构体)
   ├── 初始化回调函数
   ├── 配置DoIP参数
   ├── 调用SoAd_Doip_Init()
   │   ├── 初始化套接字管理
   │   ├── 创建UDP套接字 (端口13400)
   │   ├── 创建TCP监听套接字 (端口13400)
   │   ├── 配置多播组加入
   │   ├── 初始化DoIP状态机
   │   └── 启动主循环线程
   └── 返回初始化结果
```

### 5.3 DoIP连接状态机

DoIP系统的连接状态管理采用状态机模式，清晰地定义了各种连接状态之间的转换关系：

```mermaid
stateDiagram-v2
    [*] --> SOCKET_UNINIT : 系统启动

    SOCKET_UNINIT --> SOCKET_INIT : 套接字初始化
    SOCKET_INIT --> SOCKET_UDP_READY : UDP套接字创建成功
    SOCKET_INIT --> SOCKET_TCP_LISTENING : TCP套接字监听成功
    SOCKET_INIT --> SOCKET_DUPLICATE : 套接字重复

    SOCKET_UDP_READY --> SOCKET_UDP_READY : UDP消息处理
    note right of SOCKET_UDP_READY
        处理车辆识别请求
        处理实体状态请求
        处理电源模式请求
    end note

    SOCKET_TCP_LISTENING --> SOCKET_TCP_READY : TCP连接建立
    note right of SOCKET_TCP_LISTENING
        等待诊断仪连接
        端口13400监听
    end note

    SOCKET_TCP_READY --> DOIP_SOCKET_REGISTERED : 路由激活成功
    SOCKET_TCP_READY --> SOCKET_TCP_LISTENING : 连接断开/超时

    state DOIP_SOCKET_REGISTERED {
        [*] --> Authenticated : 认证检查
        [*] --> Confirmed : 确认检查
        [*] --> Active : 激活状态

        Authenticated --> Active : 认证成功
        Confirmed --> Active : 确认成功
        Active --> AliveCheck : 定时器触发
        AliveCheck --> Active : 响应正常
        AliveCheck --> Timeout : 响应超时

        state Active {
            [*] --> DiagnosticReady : 准备接收诊断
            DiagnosticReady --> ProcessingDiag : 处理诊断消息
            ProcessingDiag --> DiagnosticReady : 处理完成
        }
    }

    DOIP_SOCKET_REGISTERED --> SOCKET_TCP_LISTENING : 连接关闭
    Timeout --> SOCKET_TCP_LISTENING : 超时关闭连接

    SOCKET_DUPLICATE --> SOCKET_INIT : 重新初始化
    SOCKET_UDP_READY --> [*] : 系统关闭
    SOCKET_TCP_LISTENING --> [*] : 系统关闭
    SOCKET_TCP_READY --> [*] : 系统关闭
    DOIP_SOCKET_REGISTERED --> [*] : 系统关闭

    note left of DOIP_SOCKET_REGISTERED
        路由激活状态
        - 初始非活动定时器: 2秒
        - 通用非活动定时器: 5分钟
        - 在线检查定时器: 500ms
    end note
```

### 5.4 车辆识别流程
```
UDP接收车辆识别请求:
1. 接收UDP消息 (端口13400)
2. 解析DoIP头部
3. 验证协议版本
4. 根据消息类型处理:
   ├── PAYLOAD_TYPE_IDREQUEST: 通用车辆识别
   ├── PAYLOAD_TYPE_IDREQUEST_BY_EID: 基于EID识别
   └── PAYLOAD_TYPE_IDREQUEST_BY_VIN: 基于VIN识别
5. 构造车辆识别响应 (PAYLOAD_TYPE_ANNOUNCEMENT)
6. 通过UDP广播发送响应
```

### 5.5 路由激活流程
```
TCP路由激活处理:
1. 接收TCP连接请求
2. 接收路由激活请求 (PAYLOAD_TYPE_ROUTING_ACTIVATION)
3. 验证源地址 (SA) 合法性
4. 检查路由激活类型支持
5. 分配套接字资源
6. 执行认证检查 (如需要)
7. 执行确认检查 (如需要)
8. 构造路由激活响应
9. 发送响应并更新连接状态
10. 启动非活动定时器
```

### 5.6 诊断消息传输流程
```
诊断消息处理:
1. 接收诊断消息 (PAYLOAD_TYPE_DIAGNOSTIC_MESSAGE)
2. 解析SA和TA地址
3. 验证地址映射合法性
4. 检查连接状态
5. 提取诊断数据
6. 调用应用层回调函数
7. 处理诊断响应 (如有)
8. 构造诊断响应消息
9. 发送响应到诊断仪
```

### 5.7 在线检查机制
```
在线检查流程:
1. 定时器触发在线检查
2. 构造在线检查请求 (PAYLOAD_TYPE_ALIVE_CHECK_REQUEST)
3. 发送到所有活动连接
4. 设置等待响应标志
5. 启动响应超时定时器
6. 接收在线检查响应 (PAYLOAD_TYPE_ALIVE_CHECK_RESPONSE)
7. 验证响应SA地址
8. 重置非活动定时器
9. 清除等待响应标志
10. 超时处理: 关闭无响应连接
```

## 6. 接口定义

### 6.1 主要API接口

#### 6.1.1 初始化接口
```c
/**
 * DoIP服务接口初始化
 * @param pAppfunSoadConfig: 回调信息相关配置
 * @param pAppSoadConfig: app相关配置信息
 * @param parampath: JSON配置文件路径
 * @return: 成功返回0, 失败返回-1
 */
int DoIP_Server_Init(APPFUNCONFIG_SoAd_ConfigType *pAppfunSoadConfig,
                     APPCONFIG_SoAdDoIP_ConfigType *pAppSoadConfig,
                     char* parampath);
```

#### 6.1.2 去初始化接口
```c
/**
 * DoIP服务接口去初始化
 * @return: 成功返回0, 失败返回小于0
 */
int DoIP_Server_DeInit(void);
```

#### 6.1.3 诊断消息发送接口
```c
/**
 * 诊断消息发送接口
 * @param tpsocknr: 套接字句柄
 * @param ptr: 数据缓冲区
 * @param len: 数据长度
 * @return: 成功返回发送字节数, 失败返回负值
 */
int SoAdTp_Transmit_Dianostic(uint16 tpsocknr, uint8* ptr, uint16 len);
```

### 6.2 回调函数接口

#### 6.2.1 诊断数据回调
```c
/**
 * 诊断数据接收回调函数
 * @param recinfo: DoIP接收数据信息
 * @param recbuf: 接收数据缓冲区
 * @param reclen: 接收数据长度
 */
typedef void(*gappcallback_handle)(doiprecinfo_st recinfo, 
                                   unsigned char *recbuf, 
                                   unsigned int reclen);
```

#### 6.2.2 事件通知回调
```c
/**
 * DoIP事件通知函数
 * @param event: 事件类型
 * @param data: 附加数据
 */
typedef void(*gappDoipEventNotifier)(unsigned char event, void *data);
```

#### 6.2.3 通道状态控制回调
```c
/**
 * 以太网诊断通道占用或释放状态控制
 * @param status: 0x01-通道占用, 0x02-通道释放
 * @return: 成功返回0, 失败返回-1
 */
typedef int(*gappSetDgsChnStatus)(char status);
```

#### 6.2.4 调试信息输出回调
```c
/**
 * 调试信息输出函数
 * @param level: 调试级别
 * @param format: 格式化字符串
 * @param ...: 可变参数
 */
typedef void (*gappSoadDoipDebugMsg)(APPSOADDOIP_DEBUG_ENUM level,
                                     const char* format, ...);
```

## 7. 配置管理

### 7.1 静态配置 (doip_cfg.c)
系统提供默认的静态配置，包括:
- 套接字连接配置 (UDP端口13400, TCP端口13400)
- DoIP超时参数配置
- 目标地址和测试设备地址配置
- 路由激活配置

### 7.2 动态配置 (JSON文件)
支持通过JSON配置文件进行动态配置:
```json
{
    "VIN": "doip-vin012345679",
    "EID": 2,
    "GID": 2,
    "LogicalAddress": 37,
    "broadcastaddr": "***************",
    "multicastaddr": "**********",
    "ethname": "eth0",
    "DoipTesterSaList": [
        {"address": 3712, "numbytes": 0},
        {"address": 3716, "numbytes": 0}
    ],
    "DoipTaList": [
        {"addressValue": 37, "txPdu": 0, "rxPdu": 0},
        {"addressValue": 58368, "txPdu": 0, "rxPdu": 0}
    ],
    "ResponseTimeoutMs": 500,
    "GenericInactiveTimeMs": 300000,
    "InitialInactiveTimeMs": 2000,
    "VidAnnounceNum": 3,
    "VidAnnounceIntervalMs": 500,
    "VidAnnounceMaxWaitMs": 1500,
    "VidAnnounceMinWaitMs": 0,
    "AliveCheckresponseTimeMs": 500,
    "DoIpControlTimeoutMs": 2000
}
```

### 7.3 配置参数说明

#### 7.3.1 基本参数
- **VIN**: 车辆识别号 (17字节)
- **EID**: 实体标识符 (6字节)
- **GID**: 组标识符 (6字节)
- **LogicalAddress**: DoIP实体逻辑地址

#### 7.3.2 网络参数
- **broadcastaddr**: 广播地址
- **multicastaddr**: 多播地址 (默认**********)
- **ethname**: 网络接口名称

#### 7.3.3 地址配置
- **DoipTesterSaList**: 允许连接的诊断仪SA地址列表
- **DoipTaList**: 支持的目标地址TA列表

#### 7.3.4 超时参数
- **ResponseTimeoutMs**: 响应超时 (500ms)
- **GenericInactiveTimeMs**: 通用非活动超时 (5分钟)
- **InitialInactiveTimeMs**: 初始非活动超时 (2秒)
- **AliveCheckresponseTimeMs**: 在线检查响应超时 (500ms)
- **VidAnnounceNum**: 车辆识别声明次数 (3次)
- **VidAnnounceIntervalMs**: 声明间隔 (500ms)

## 8. 错误处理与调试

### 8.1 错误码定义
系统定义了完整的错误码体系:
- 初始化错误: -1 到 -4
- 网络错误: 套接字创建、绑定、连接失败
- 协议错误: 消息格式错误、版本不匹配
- 超时错误: 响应超时、连接超时

### 8.2 调试级别
```c
typedef enum {
    APPSOADDOIP_DEBUG_NONE = 0x0,      // 无调试信息
    APPSOADDOIP_DEBUG_ERROR = 0x01,    // 错误信息
    APPSOADDOIP_DEBUG_WARN = 0x02,     // 警告信息
    APPSOADDOIP_DEBUG_INFO = 0x04,     // 信息输出
} APPSOADDOIP_DEBUG_ENUM;
```

### 8.3 日志输出
系统提供灵活的日志输出机制，支持:
- 分级日志输出
- 自定义日志回调函数
- 运行时日志级别控制
- 详细的错误信息和状态跟踪

## 9. 性能特性

### 9.1 并发处理
- 支持多个诊断仪同时连接
- 异步消息处理机制
- 线程安全的状态管理
- 高效的套接字事件处理 (epoll)

### 9.2 资源管理
- 动态内存管理
- 缓冲区池化机制
- 连接资源限制
- 超时自动清理

### 9.3 网络优化
- TCP_NODELAY选项支持
- 套接字重用机制
- 多播组管理
- 广播优化

## 10. 测试与验证

### 10.1 测试框架
项目提供完整的测试代码 (`test_code/`):
- 单元测试
- 集成测试
- 性能测试
- 兼容性测试

### 10.2 测试用例
- DoIP协议一致性测试
- 网络连接稳定性测试
- 超时机制验证
- 错误处理测试
- 多客户端并发测试

## 11. 部署与维护

### 11.1 编译构建
```bash
# 编译主库
make

# 编译测试程序
cd test_code
make
```

### 11.2 运行要求
- Linux操作系统
- 网络接口支持
- 端口13400可用
- 足够的内存资源

### 11.3 配置部署
1. 准备JSON配置文件
2. 设置网络接口和路由
3. 配置防火墙规则
4. 启动DoIP服务

### 11.4 监控维护
- 连接状态监控
- 性能指标收集
- 日志分析
- 故障诊断

## 12. 扩展性设计

### 12.1 模块化设计
系统采用高度模块化的设计，便于:
- 功能模块独立开发
- 接口标准化
- 组件可替换性
- 系统可扩展性

### 12.2 配置灵活性
- 支持静态和动态配置
- 运行时参数调整
- 多种配置源支持
- 配置热更新机制

### 12.3 协议扩展
- 支持新的DoIP消息类型
- 自定义协议扩展
- 版本兼容性管理
- 向后兼容保证

## 13. 图表说明

本设计方案文档使用了Mermaid图表来直观展示系统架构和流程：

### 13.1 分层架构图
- **图表类型**: 流程图 (graph TB)
- **展示内容**: DoIP系统的五层架构设计，包括各层的主要组件和层间依赖关系
- **颜色编码**: 不同颜色代表不同的架构层次，便于区分和理解

### 13.2 消息处理流程图
- **图表类型**: 流程图 (graph TD)
- **展示内容**: DoIP消息从接收到处理完成的完整流程
- **决策节点**: 橙色菱形表示判断条件
- **处理节点**: 蓝色矩形表示处理步骤
- **错误处理**: 红色节点表示错误情况

### 13.3 连接状态机图
- **图表类型**: 状态图 (stateDiagram-v2)
- **展示内容**: DoIP连接的各种状态及其转换条件
- **嵌套状态**: 展示了路由激活状态下的子状态
- **注释说明**: 包含了重要的定时器参数信息

### 13.4 模块关系图
- **图表类型**: 流程图 (graph TB)
- **展示内容**: 各个源文件模块之间的依赖关系
- **分组显示**: 按功能层次对模块进行分组
- **依赖箭头**: 清晰显示模块间的调用和依赖关系

这些图表可以在支持Mermaid的Markdown查看器中正常显示，如GitHub、GitLab、Typora等。

---

**文档版本**: V1.1
**最后更新**: 2024年
**维护团队**: 厦门雅迅网络股份有限公司
**图表工具**: Mermaid
