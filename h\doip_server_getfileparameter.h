/********************************************************************************
**
** 文件名:     doip_server_getfileparameter.h
** 版权所有:   (c) 2022-2028 厦门雅迅网络股份有限公司
** 文件描述:   doip服务端获取配置文件参数
**
*********************************************************************************
**             修改历史记录
**===============================================================================
**| 日期       | 作者   |  修改记录
**===============================================================================
**| 2025/02/18 | yx |  创建该文件
**
*********************************************************************************/
#ifndef __DOIP_SERVER_GETFILEPARAMETER_H__
#define __DOIP_SERVER_GETFILEPARAMETER_H__

#ifdef __cplusplus
extern "C"
{
#endif

typedef struct {
	int address;
	int numbytes;
} DoipTesterSaEntry;

typedef struct {
	int addressValue;
	int txPdu;
	int rxPdu;
} DoipTaEntry;

typedef struct {
	char *VIN;
	long long EID;
	long long GID;
	char *broadcastaddr;
	char *multicastaddr;
	char *ethname;
	int LogicalAddress;
	DoipTesterSaEntry *DoipTesterSaList;
	int DoipTesterSaCount;
	DoipTaEntry *DoipTaList;
	int DoipTaCount;
	int ResponseTimeoutMs;
	int GenericInactiveTimeMs;
	int InitialInactiveTimeMs;
	int VidAnnounceNum;
	int VidAnnounceIntervalMs;
	int VidAnnounceMaxWaitMs;
	int VidAnnounceMinWaitMs;
	int AliveCheckresponseTimeMs;
	int DoIpControlTimeoutMs;
} Config;

Config* parse_config(const char *json_str);
Config* parse_config_from_file(const char *filename);

#ifdef __cplusplus
}
#endif

#endif      /* End Of __DOIP_SERVER_GETFILEPARAMETER_H__ */

