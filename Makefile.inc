
#include path
INCLUDES    += -I./h
INCLUDES    += -I./src_h

INSTALL_SRC_INC:=./h/*
INSTALL_DES_INC:=$(SDK_EVN_ENTRY)/env/include/doips/

INSTALL_SRC_AR:=./_build/*
INSTALL_DEC_AR:= $(SDK_EVN_ENTRY)/env/lib/usrlib

# source files, sub directory will then add to this, all source files must use ".c" as the extension name        
SOURCES += c/cjson.c \
		c/doip_server_appconfig.c \
		c/doip_server_getfileparameter.c \
		c/doip_server.c \
		src_c/doip_cfg.c \
		src_c/soad_doip.c \
		src_c/soad.c

# sub directories, sub directory will then add to this
SUBDIRS		+= c

