
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <pthread.h>
#include <assert.h>
#include <stdarg.h>

#include "doip_server.h"
//#include "yxdc_types.h"

#define SDPE_DOCAN_TEST  0              /** sdpe_docan 测试 **/
#define DOIP_SERVER_TEST 1              /** eth doip server 测试 **/
#define CLIENT_DOIP_TEST 0              /** eth doip client 测试 **/

static int sg_test_api = 3;
pthread_t           test_pthread;                /* linux线程标识符 */
pthread_mutex_t     test_mutex;
static int sg_looprun = 0,sg_testnum = 0; /** 模拟循环测试 **/






/*****************************************************************/
/***********************    分割线      **********************/
/***********************    以下为以太网DOIP测试           ****************************/
/*****************************************************************/


#include "doip_server.h"
#include "doip_server_appconfig.h"

#if DOIP_SERVER_TEST

#define DEVCIES_VIN_LEN  17
//#define DEVCIES_VIN_VAL  "doip-vin012345679"
char devices_vin_val[] = "doip-vin012345679";
#define DEVCIES_EID_LEN  6
#define DEVCIES_EID_VAL  0x000000000002
#define DEVCIES_GID_LEN  6
#define DEVCIES_GID_VAL  0x000000000002

/** 定义sa地址个数，及地址值 **/
#define TESTER_SA_NUM   2
APPCONFIG_DoIp_TesterConfigType Tester_sa[TESTER_SA_NUM] = {
	{
		.address = 0x0e80,
		.numBytes = 0,             /** 该值暂时填充为0 **/
	},
	{
		.address = 0x0e84,
		.numBytes = 0,             /** 该值暂时填充为0 **/
	}
};

/** 定义ta地址个数，及地址值 **/
#define TARGET_TA_NUM   2
APPCONFIG_DoIp_TargetAddressConfigType Target_ta[TARGET_TA_NUM] = {
	{
		.addressValue = 0x0025,
		.txPdu = 0,               /** 该值暂时填充为0 **/
		.txPdu = 0,               /** 该值暂时填充为0 **/
	},
	{
		.addressValue = 0xe400,
		.txPdu = 0,              /** 该值暂时填充为0 **/
		.txPdu = 0,              /** 该值暂时填充为0 **/
	}
};

/** 定义广播地址，及加入的多播组IP **/
/** 注意：添加“***************”的广播地址时，需要添加路由信息 **/
/** 添加广播地址时，需在控制台执行如下命令,具体IP跟网卡选择以实际设置为准 **/
/** route add *************** dev eth0 **/
/** 添加多播地址时，需在控制台执行如下命令,具体IP跟网卡选择以实际设置为准 **/
/** route add -net *********/24 dev eth0 **/

static char broadcastaddress[16] = "*************";//"*************";//"***************";//"*************";
static char multicastaddress[16] = "**********";

static APPCONFIG_SoAdDoIpTimeConfigType	sgiDoIpConfig =
{
	.DoIpAliveCheckresponseTimeMs  = 500,
	.DoIpControlTimeoutMs          = 2000,
	.DoIpGenericInactiveTimeMs     = 1000 * 60 * 5,
	.DoIpInitialInactiveTimeMs     = 2000,
	.DoIpResponseTimeoutMs         = 500,
	.DoIpVidAnnounceIntervalMs     = 500,
	.DoIpVidAnnounceMaxWaitMs      = 500*3,
	.DoIpVidAnnounceMinWaitMs      = 0,
	.DoIpVidAnnounceNum            = 3,
};

/** DOIP服务配置相关信息 **/
static APPCONFIG_SoAdDoIP_ConfigType sg_appconf_info = {
	.vin_len = DEVCIES_VIN_LEN,
	.vin_val = devices_vin_val,   /** 注意 VIN为指针 **/
	.eid_len = DEVCIES_EID_LEN,
	.eid_val = DEVCIES_EID_VAL,
	.gid_len = DEVCIES_GID_LEN,
	.gid_val = DEVCIES_GID_VAL,
	.DoIpNodeLogicalAddress = 0x25,
	.DoIpTesters_num = TESTER_SA_NUM,
	.DoIpTesters = Tester_sa,
	.DoIpTargetAddresses_num = TARGET_TA_NUM,
	.DoIpTargetAddresses = Target_ta,
	.broadcast_len = 16,
	.broadcast_address = broadcastaddress,
	.multicast_len = 16,
	.multicast_address = multicastaddress,
	.ethname = "bridge0",
	.eid_len = 7,
	.SoAdDoIpTime = &sgiDoIpConfig
};


/** 定义路由激活种类个数，及认证函数 **/
#define ROUTINGACTIVATION_NUM   3
APPCONFIG_DoIp_RoutingActivationConfigType RoutingActivation[ROUTINGACTIVATION_NUM] = {
	{
		.activationNumber = 0x00,
		.authenticationCallback = NULL,
		.confirmationCallback = NULL,
	},
	{
		.activationNumber = 0x01,
		.authenticationCallback = NULL,
		.confirmationCallback = NULL,
	},
	{
		.activationNumber = 0x0e,
		.authenticationCallback = NULL,
		.confirmationCallback = NULL,
	}
};

int testappSetDgsChnStatus(char status);
void testappDoipEventNotifier(unsigned char event, void *data);
void testappcallback_handle(doiprecinfo_st rec, unsigned char *, unsigned int);
void testappSoadDoipDebugMsg(APPSOADDOIP_DEBUG_ENUM bugenum,const char* format,...);


static APPFUNCONFIG_SoAd_ConfigType sg_AppfunSoadConfig = {
	.debugstatus = APPSOADDOIP_DEBUG_INFO|APPSOADDOIP_DEBUG_WARN|APPSOADDOIP_DEBUG_ERROR,
	.SetDgsChnStatus = testappSetDgsChnStatus,
	.DoipEventNotifier = testappDoipEventNotifier,			   /** doip层事件上报接口 **/
	.callback_handle = testappcallback_handle,			   /** 诊断数据回调接口 **/
	.pDoIPServerDiagnosticMsg = NULL,	   /** 数据回调接口,注意该接口应用层无需注册，初始化为NULL **/
	.soaddoip_debugmsg = testappSoadDoipDebugMsg,             /** 调试信息输出的函数指针 **/
	.routingactivenum = ROUTINGACTIVATION_NUM,
	.routingactivearry = RoutingActivation,
};

/**********************************************************
 * 函数名称:	testappSetDgsChnStatus
 * 函数作用:	DoIP设置以太网诊断通道占用或释放状态
 * 函数参数:	[in] sync:		0x01:表示以太网通道占用，0x02:表示以太网通道释放
 * 函数返回:	占用或释放成功返回为0，失败返回-1
**********************************************************/
int testappSetDgsChnStatus(char status)
{
	if(status == 0x01){
		//如果允许通道占用，则返回0,否则返回-1
		printf("allow channel occupy!!\n");
		return 0;
	}else if(status == 0x02){
		//如果允许通道释放，则返回0,否则返回-1
		printf("allow channel release!!\n");
		return 0;
	}else{
		//状态类型错误
		printf("status error!!\n");
		return -1;
	}
	return -1;
}

/**********************************************************
 * 函数名称:	testappDoipEventNotifier
 * 函数作用:	DoIP事件通知函数指针
 * 函数参数:	[in] event:		事件类型
 * 函数参数:	[in] data:		附加数据
 * 函数返回:	无
**********************************************************/
void testappDoipEventNotifier(unsigned char event, void *data)
{
	printf("%s,%d,event ID = %d\n",__func__,__LINE__,event);
}


/** 接收以太网诊断数据回调接口处理函数 **/
/** buf第一个字节为SID，第二字节之后为数据内容 **/
void testappcallback_handle(doiprecinfo_st tpdrecinfo, unsigned char *buf, unsigned int buflen)
{
	printf("%s,%d,buflen = %d,buf = ",__func__,__LINE__,buflen);
	int i = 0;
	for(i = 0 ;i < buflen; i++){
		printf("%02X ",buf[i]);
	}
	printf("\n");
	//doipsndinfo_st sndinfo;
	//sndinfo.sa = tpdrecinfo.ta;
	//sndinfo.ta = tpdrecinfo.sa;
	unsigned sockNr = tpdrecinfo.sockNr;

	unsigned char sndbuf[512] = {0};
	i = 0;

	sndbuf[i++] = tpdrecinfo.ta>>8;
	sndbuf[i++] = tpdrecinfo.ta&0xff;
	sndbuf[i++] = tpdrecinfo.sa>>8;
	sndbuf[i++] = tpdrecinfo.sa&0xff;
	
	switch (buf[0]){ //ServerID
		case 0x10:
			sndbuf[i++] = buf[0] + 0x40;
			sndbuf[i++] = buf[1];
			break;
		case 0x27:
			sndbuf[i++] = buf[0] + 0x40;
			sndbuf[i++] = buf[1];
			if(buf[1] == 0x01 || buf[1] == 0x03 || buf[1] == 0x07){
				memset(&sndbuf[i],0xaa,40);
				i+=40;
			}else{
				
			}
			break;
		case 0x22:
			sndbuf[i++] = buf[0] + 0x40;
			printf("%s,%d,version:\n",__func__,__LINE__);
			if((buf[1] == 0xF1) && (buf[2] == 0x95)){
				printf("version:\n");  //verison : v1.0.1
				sndbuf[i++] = buf[1];
				sndbuf[i++] = buf[2];
				sndbuf[i++] = 0x56;
				sndbuf[i++] = 0x31;
				sndbuf[i++] = 0x2E;
				sndbuf[i++] = 0x30;
				sndbuf[i++] = 0x2E;
				sndbuf[i++] = 0x31;
			}
			break;
		case 0x3E:
			printf("%s,%d,3e\n",__func__,__LINE__);
			return;
		default:
			break;
	}

	DoIP_Server_Diagnostic_Send_Msg(sockNr,sndbuf,i);    /** 诊断数据发送接口 **/
}

void testappSoadDoipDebugMsg(APPSOADDOIP_DEBUG_ENUM bugenum,const char* format,...)
{
    va_list args;
    va_start(args, format); // 初始化args来存储可变参数

    if(bugenum == APPSOADDOIP_DEBUG_INFO){
        vprintf(format, args); // 使用vprintf来处理可变参数
    }else if(bugenum == APPSOADDOIP_DEBUG_WARN){
        vprintf(format, args); // 使用vprintf来处理可变参数
    }else if(bugenum == APPSOADDOIP_DEBUG_ERROR){
        vprintf(format, args); // 使用vprintf来处理可变参数
    }

    va_end(args); // 清理args
}



#endif

void* test_api_handle(void *pdata)
{
	int testmode = 0;
	int ctlret = 0;
	ctlret = ctlret;
	int period_time = 5000000; /** 模拟测试周期微秒 **/
	int tiii = 10;
	while(1){
		assert(pthread_mutex_lock(&test_mutex) == 0);
    	testmode = sg_test_api;
		assert(pthread_mutex_unlock(&test_mutex) == 0);
		switch(testmode){
			case 1:
				sg_looprun = 1;
				break;
			case 2:
				break;
			case 21:
				break;
			case 3:
				#if DOIP_SERVER_TEST /** 以太网doip服务端测试 **/
				DoIP_Server_Init(&sg_AppfunSoadConfig,&sg_appconf_info,NULL);
				#endif
				break;
			case 31:
				#if DOIP_SERVER_TEST /** 以太网doip服务端测试 **/
				DoIP_Server_DeInit();
				#endif
				break;
			case 4:
				break;
			case 41:
				break;
			case 5:
				DoIP_Server_Init(&sg_AppfunSoadConfig,NULL,"/root/yxapp/config.json");
				break;
			case 6:
				break;
			case 7:
				break;
			default:
				break;
		}
		if(sg_looprun == 1){
			int testarray[]={2,3,4,21,31,41};
			if( sg_testnum >= sizeof(testarray)/sizeof(testarray[0])){
				sg_testnum = 0;
			}
			sg_test_api = testarray[sg_testnum++];
			usleep(period_time);
		}else{
			tiii++;
			if(tiii > 10){
				printf("%s,%d,docanip test\n",__func__,__LINE__);
				tiii = 1;
			}
			assert(pthread_mutex_lock(&test_mutex) == 0);
	    	sg_test_api = 0;
			assert(pthread_mutex_unlock(&test_mutex) == 0);
			sleep(1);
		}
	}
}

int main(int argc,char* argv[])
{
	printf("*****testdoips=****** \n");
	//doips_entry_init(argc,argv);

	char version[64] = {0};
	int vlen = 64;
	DoIP_Server_Version(version,&vlen);
	printf("%s,%d,%s\n",__func__,__LINE__,version);

	int s,ti;
	s = pthread_mutex_init(&test_mutex, NULL);
	s = pthread_create(&test_pthread, NULL, test_api_handle, (void *)&ti);  /* 创建linux线程 */
    assert(s == 0);
	int testmode = 0;
	printf("input a number,to chose test api!!!\n");
	printf("input 1 ---> loop test \n");
	printf("input 2 ---> sdpe doip init\n");
	printf("input 21 ---> sdpe doip deinit,exit\n");
	printf("input 3 ---> doip server init\n");
	printf("input 31 ---> doip server  deinit,exit\n");
	printf("input 4 ---> doip tcp client init\n");
	printf("input 41 ---> doip tcp client  deinit,exit\n");
	while(1){
		#if 0
		printf("*****testmode=****** \n");
		scanf("%d",&testmode);
		assert(pthread_mutex_lock(&test_mutex) == 0);
    	sg_test_api = testmode;
		assert(pthread_mutex_unlock(&test_mutex) == 0);
		printf("sg_test_api = %d\n",sg_test_api);
		#else
		sleep(1);
		#endif
	}
	return 0;
}
