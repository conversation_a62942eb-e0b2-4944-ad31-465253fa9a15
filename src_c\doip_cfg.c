#include <stddef.h>
#include "platform_types.h"
#include "soad_types.h"
#include "soad_internal.h"


static SoAd_SocketConnectionType iSocketConnection[SOAD_SOCKET_COUNT] =
{
	{
		.SocketId           		= 0,
		.SocketLocalIpAddress  		= "0.0.0.0",
		.SocketLocalPort     		= 13400,						//tcp to receive
		.SocketRemoteIpAddress 		= "0.0.0.0",
		.SocketRemotePort  			= 0,			   				//tcp to send
		.SocketProtocol   			= SOAD_SOCKET_PROT_UDP,
		.SocketTcpInitiate 			= false,	  					//not use
		.SocketTcpNoDelay 			= false,						//not use
		.SocketUdpListenOnly 		= false,   						//not use
		.AutosarConnectorType  		= SOAD_AUTOSAR_CONNECTOR_DOIP,
		.PduHeaderEnable			= false,
		.SocketAutosarApi   		= false,
		.ResourceManagementEnable 	= false,
		.PduProvideBufferEnable 	= false
	},

	{
		.SocketId           		= 1,
		.SocketLocalIpAddress  		= "0.0.0.0",
		.SocketLocalPort     		= 13400,						//tcp to receive
		.SocketRemoteIpAddress 		= "0.0.0.0",
		.SocketRemotePort  			= 0,			   				//tcp to send
		.SocketProtocol   			= SOAD_SOCKET_PROT_TCP,
		.SocketTcpInitiate 			= false,	  					//not use
		.SocketTcpNoDelay 			= false,						//not use
		.SocketUdpListenOnly 		= true,   						//not use
		.AutosarConnectorType  		= SOAD_AUTOSAR_CONNECTOR_DOIP,
		.PduHeaderEnable			= false,
		.SocketAutosarApi   		= false,
		.ResourceManagementEnable 	= false,
		.PduProvideBufferEnable 	= false
	},

	{
		.SocketId           		= 2,
		.SocketLocalIpAddress  		= "0.0.0.0",
		.SocketLocalPort     		= 13400,						//tcp to receive
		.SocketRemoteIpAddress 		= "0.0.0.0",
		.SocketRemotePort  			= 0,			   				//tcp to send
		.SocketProtocol   			= SOAD_SOCKET_PROT_TCP,
		.SocketTcpInitiate 			= false,	  					//not use
		.SocketTcpNoDelay 			= false,						//not use
		.SocketUdpListenOnly 		= false,   						//not use
		.AutosarConnectorType  		= SOAD_AUTOSAR_CONNECTOR_DOIP,
		.PduHeaderEnable			= false,
		.SocketAutosarApi   		= false,
		.ResourceManagementEnable 	= false,
		.PduProvideBufferEnable 	= false
	},

	{
		.SocketId           		= 3,
		.SocketLocalIpAddress  		= "0.0.0.0",
		.SocketLocalPort     		= 13400,						//tcp to receive
		.SocketRemoteIpAddress 		= "0.0.0.0",
		.SocketRemotePort  			= 0,			   				//tcp to send
		.SocketProtocol  	 		= SOAD_SOCKET_PROT_TCP,
		.SocketTcpInitiate 			= false,	  					//not use
		.SocketTcpNoDelay 			= false,						//not use
		.SocketUdpListenOnly 		= false,   						//not use
		.AutosarConnectorType  		= SOAD_AUTOSAR_CONNECTOR_DOIP,
		.PduHeaderEnable			= false,
		.SocketAutosarApi   		= false,
		.ResourceManagementEnable 	= false,
		.PduProvideBufferEnable 	= false
	},

#if defined(USE_UDPNM)
	{
		.SocketId           		= 3,
		.SocketLocalIpAddress  		= "0.0.0.0",
		.SocketLocalPort     		= 15001,						//tcp to receive
		.SocketRemoteIpAddress 		= "***************",
		.SocketRemotePort  			= 15001,			   			//tcp to send
		.SocketProtocol   			= SOAD_SOCKET_PROT_UDP,
		.SocketTcpInitiate 			= false,	  					//not use
		.SocketTcpNoDelay 			= false,						//not use
		.SocketUdpListenOnly 		= false,   						//not use
		.AutosarConnectorType  		= SOAD_AUTOSAR_CONNECTOR_UDPNM,
		.PduHeaderEnable			= false,
		.SocketAutosarApi   		= false,
		.ResourceManagementEnable 	= false,
		.PduProvideBufferEnable 	= false
	}
#endif
};

typedef Std_ReturnType (*SoAd_CallbackDoIpPowerModeFncType)(SoAd_DoIp_PowerMode* powermode);

// 0 - not ready, 1 - ready, 2 - not support
Std_ReturnType SoAd_DoIpPowerModeFncType(SoAd_DoIp_PowerMode* powermode)
{
	*powermode  = 1;
	//FK_TRACE_INFO("SoAd_DoIpPowerModeFncType power mode: %d \r\n", *powermode);
	SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"SoAd_DoIpPowerModeFncType power mode: %d \r\n", *powermode);
	return E_OK;
}

SoAd_DoIpConfigType	iDoIpConfig =
{
	.DoIpAliveCheckresponseTimeMs  = 500,
	.DoIpControlTimeoutMs          = 2000,
	.DoIpGenericInactiveTimeMs     = 1000 * 60 * 5,
	.DoIpHostNameOpt               = (uint8 *)"DoIP-VIN12345678901234567",
	.DoIpInitialInactiveTimeMs     = 2000,
	.DoIpResponseTimeoutMs         = 500,
	.DoIpVidAnnounceIntervalMs     = 500,
	.DoIpVidAnnounceMaxWaitMs      = 500*3,
	.DoIpVidAnnounceMinWaitMs      = 0,
	.DoIpVidAnnounceNum            = 3,
	.DoipPowerModeCallback         = SoAd_DoIpPowerModeFncType
};

DoIp_TesterConfigType iDoIpTesters[DOIP_TESTER_COUNT] =
{
	{
		.address   = 0x0E80, 	//tbd   local tester physic address
		.numBytes  = 0     		//tbd
	},

	{
		.address   = 0x0E81, 	//tbd   remote tester physic address
		.numBytes  = 0     		//tbd
	},

	{
		.address   = 0x0E82, 	//tbd   remote tester physic address
		.numBytes  = 0     		//tbd
	},
};

/** 路由激活类型有两种，ta地址有三种，所以各自的索引映射表应该有6组，(0,0),(1,0),(0,1),(1,1),(0,2),(1,2) **/
DoIp_RoutingActivationToTargetAddressMappingType iDoIpRoutingActivationToTargetAddressMap[DOIP_ROUTINGACTIVATION_TO_TARGET_RELATION_COUNT]  =
{
	{
		.routingActivation    = 0,  	//tbd	the number of iDoIpRoutingActivations   routingActivationIndex
		.target               = 0   	//tbd	the number of iDoIpTargetAddresses
    },

	{
		.routingActivation    = 1,  	//tbd
		.target               = 0   	//tbd
    },

#if DOIP_ROUTINGACTIVATION_TO_TARGET_RELATION_COUNT > 3
	{
		.routingActivation    = 0,  	//tbd	the number of iDoIpRoutingActivations   routingActivationIndex
		.target               = 1   	//tbd	the number of iDoIpTargetAddresses
    },

	{
		.routingActivation    = 1,  	//tbd
		.target               = 1   	//tbd
    },

	{
		.routingActivation    = 0,  	//tbd	the number of iDoIpRoutingActivations   routingActivationIndex
		.target               = 2   	//tbd	the number of iDoIpTargetAddresses
    },

	{
		.routingActivation    = 1,  	//tbd
		.target               = 2  		//tbd
    },
#endif
};

//iDoIpRoutingActivationToTargetAddressMap is  associate iDoIpRoutingActivations with iDoIpTargetAddresses
DoIp_RoutingActivationConfigType iDoIpRoutingActivations[DOIP_ROUTINGACTIVATION_COUNT] =
{
	{
		.activationNumber          = 0xe0,		//active type: central security
		.authenticationCallback    = NULL,
		.confirmationCallback      = NULL
	},

	{
		.activationNumber          = 0x00,		// active type: default
		.authenticationCallback    = NULL,
		.confirmationCallback      = NULL
	},
	
#if DOIP_ROUTINGACTIVATION_COUNT > 2
	{
		.activationNumber          = 0x01,		// active type: obd
		.authenticationCallback    = NULL,
		.confirmationCallback      = NULL
	},
#endif
};

DoIp_TargetAddressConfigType iDoIpTargetAddresses[DOIP_TARGET_COUNT] =
{
	{
		.addressValue   = 0x0011,
		.txPdu          = 0,
		.rxPdu          = 0
	},

	{
		.addressValue   = 0xE400,
		.txPdu          = 0,
		.rxPdu          = 0
	},

	{
		.addressValue   = 0xE401,
		.txPdu          = 0,
		.rxPdu          = 0
	},
};

SoAd_SocketRouteType   iSocketRoute[SOAD_SOCKET_ROUTE_COUNT] =
{
	{
		.SourceSocketRef      = &iSocketConnection[0],
		.SourceId             = 0,
		.DestinationPduId     = 0,								//not use
		.DestinationSduLength = 0,								//not use
		.UserRxIndicationUL   = SOAD_UL_DOIP					// not use
    },

    {
		.SourceSocketRef      = &iSocketConnection[1],
		.SourceId             = 1,
		.DestinationPduId     = 0,								//not use
		.DestinationSduLength = 0,								//not use
		.UserRxIndicationUL   = SOAD_UL_DOIP					// not use
    },

	{
		.SourceSocketRef      = &iSocketConnection[2],
		.SourceId             = 2,								//sourceid equal to iSocketConnection num
		.DestinationPduId     = 0,								//not use
		.DestinationSduLength = 8,
		.UserRxIndicationUL   = SOAD_UL_DOIP					// not use
	},

#if SOAD_SOCKET_ROUTE_COUNT > 3
	{
		.SourceSocketRef      = &iSocketConnection[3],
		.SourceId             = 4,								//sourceid equal to iSocketConnection num
		.DestinationPduId     = 0,								//not use
		.DestinationSduLength = 8,
		.UserRxIndicationUL   = SOAD_UL_DOIP					// not use
	},
#endif

#if defined(USE_UDPNM)
	{
		.SourceSocketRef      = &iSocketConnection[2],
		.SourceId             = 2,								//sourceid equal to iSocketConnection num
		.DestinationPduId     = 0,								//not use
		.DestinationSduLength = 8,
		.UserRxIndicationUL   = SOAD_UL_UDPNM					// not use
	}
#endif
};

DoIp_TesterToRoutingActivationMapType iTesterToRoutingActivationMapType[DOIP_ROUTINGACTIVATION_COUNT] =
{
	{
		.tester = 0,
		.routingActivation = 0
	},

	{
		.tester = 1,
		.routingActivation = 0
	},
};

SoAd_ConfigType gSoAdConfig =
{
	.SocketConnection                           = iSocketConnection,
	.SocketRoute                                = iSocketRoute,
	.PduRoute                                   = NULL,
	.DoIpConfig                                 = &iDoIpConfig,
	.DoIpNodeType                               = SOAD_DOIP_NODETYPE_NODE,
	.DoIpNodeLogicalAddress                     = 0x0011,										/* DoIP逻辑地址 */
	.DoIpTargetAddresses                        = iDoIpTargetAddresses,
	.DoIpRoutingActivations                     = iDoIpRoutingActivations,
	.DoIpRoutingActivationToTargetAddressMap    = iDoIpRoutingActivationToTargetAddressMap,
	.DoIpTesters                                = iDoIpTesters,
	.DoIpTesterToRoutingActivationMap           = iTesterToRoutingActivationMapType,
};