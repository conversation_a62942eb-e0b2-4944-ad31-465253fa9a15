/*-------------------------------- Arctic Core ------------------------------
 * Copyright (C) 2013, ArcCore AB, Sweden, www.arccore.com.
 * Contact: <<EMAIL>>
 *
 * You may ONLY use this file:
 * 1)if you have a valid commercial ArcCore license and then in accordance with
 * the terms contained in the written license agreement between you and ArcCore,
 * or alternatively
 * 2)if you follow the terms found in GNU General Public License version 2 as
 * published by the Free Software Foundation and appearing in the file
 * LICENSE.GPL included in the packaging of this file or here
 * <http://www.gnu.org/licenses/old-licenses/gpl-2.0.txt>
 *-------------------------------- Arctic Core -----------------------------*/
#include <string.h>
#include <assert.h>
#include <sys/select.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <net/if.h>   
#include <sys/ioctl.h>  
#include <netinet/in.h>  
#include <arpa/inet.h> 
#include <pthread.h>
#include <errno.h>
#include <stdlib.h>
#include <stddef.h>
#include "soad.h"
#include "soad_internal.h"
#include "doip_server.h"

// Diagnostic message negative acknowledge codes
typedef enum {
	// 0x00~0x01  Reserved by document
	DOIP_E_DIAG_INVALID_SA			= 0x02,			// Invalid Source Address(关闭套接字)
	DOIP_E_DIAG_UNKNOWN_TA			= 0x03,			// Unknown Target Address(丢弃DoIP报文)
	DOIP_E_DIAG_MESSAGE_TO_LARGE	= 0x04,			// Diagnostic Message too large(丢弃DoIP报文)
	DOIP_E_DIAG_OUT_OF_MEMORY		= 0x05,			// Out of memory(丢弃DoIP报文)
	DOIP_E_DIAG_TARGET_UNREACHABLE	= 0x06,			// Target unreachable(丢弃DoIP报文)
	DOIP_E_DIAG_UNKNOWN_NETWORK		= 0x07,			// Unknown network(丢弃DoIP报文)
	DOIP_E_DIAG_TP_ERROR			= 0x08,			// Transport protocol error(丢弃DoIP报文)
	// 0x09~0xFF  Reserved by document
} E_DoIPDiagNAck;

#define VIN_LEN 	17								/* VIN码长度 */
#define EID_LEN 	6								/* EID长度 */
#define GID_LEN 	6								/* GID长度 */
#define VIN  		"doip-vin012345678"				/* 默认VIN码 */
#define EID  		0x000000000001					/* 默认EID(若无特别要求, 一般为MAC地址) */
#define GID  		0x000000000001					/* 默认GID */

#define  DOIP_UDP_PORT_MIN	49152					/* 外部诊断设备发送UDP消息，应该在[49152~65535]范围内动态分配端口 */
#define  DOIP_UDP_PORT_MAX	65535

//table 38
#define SOAD_DOIP_ANNOUNCE_INTERVAL				gSoAdConfig.DoIpConfig->DoIpVidAnnounceIntervalMs  		//500ms
#define SOAD_DOIP_ANNOUNCE_NUM					gSoAdConfig.DoIpConfig->DoIpVidAnnounceNum				//车辆声明次数, 3次
#define SOAD_DOIP_ANNOUNCE_SOCKET				gSoAdConfig.SocketConnection[0].SocketId
#define DOIP_ALIVECHECK_RESPONSE_TIMEOUT		gSoAdConfig.DoIpConfig->DoIpAliveCheckresponseTimeMs	//500ms
#define DOIP_GENERAL_INACTIVITY_TIMEOUT			gSoAdConfig.DoIpConfig->DoIpGenericInactiveTimeMs  		//5min
#define DOIP_INITIALINACTIVETIMEMS				gSoAdConfig.DoIpConfig->DoIpInitialInactiveTimeMs   	//2s

#define SOAD_DOIP_ANNOUNCE_WAIT					500			//ms

#define REFRESH_GENERAL_INACTIVITY_TIMER		1			/* 是否刷新超时定时器, 0:关闭, 1:开启 */

typedef struct {
	PduInfoType *	dianostic_pduinfo;
	PduIdType		dianostic_pdutype;
	uint16			sockNr;
	uint16			sa;
	uint16			ta;
	int				ConnectionHandle;
} DianosticType;

typedef enum {
	LOOKUP_SA_TA_OK,
	LOOKUP_SA_TA_TAUNKNOWN,
	LOOKUP_SA_TA_SAERR
} LookupSaTaResultType;

typedef enum {
	DOIP_GATEWAY,			/* DoIP网关 */
	DOIP_NODE				/* DoIP实体 */
} SOAD_DOIP_NODE_TYPE;							/* 节点类型 */

typedef enum {
	DOIP_LINK_UP,
	DOIP_LINK_DOWN,
} DOIP_LINK_STATUS;

int gSoadRecvDebugLevel = 4;        /** debug level 0-4,0:highest 4:lowest **/
int gSoadSndDebugLevel  = 4;        /** debug level 0-4,0:highest 4:lowest **/

#if 0
static pthread_mutex_t	gMutexSend 						= PTHREAD_MUTEX_INITIALIZER;
static DOIP_LINK_STATUS	gLinkStatus 					= DOIP_LINK_DOWN;
static uint32 			gDoipArcAnnouncementTimer		= 0;
static uint16 			gDoipArcAnnounceWait;

static uint8			gDoipTesterCnt 					= DOIP_TESTER_COUNT;
static uint8			gDoipTargetCnt 					= DOIP_TARGET_COUNT;
static uint8			gDoipMaxTesterConnection		= DOIP_MAX_TESTER_CONNECTIONS;
static uint8			gDoipRoutingActivationCnt		= DOIP_ROUTINGACTIVATION_COUNT;

static uint8 			gVinLen							= VIN_LEN;
static uint8 * 			gpVinValue						= NULL;
static uint8 			gEidLen							= EID_LEN;
static uint64 			gEidValue						= EID;
static uint8 			gGidLen							= GID_LEN;
static uint64			gGidValue						= GID;

static uint16			gNumAnnouncements				= 0;			/** add by fly,******** **/


// Table maintained
// static DoIp_ArcDoIpSocketStatusType gpConnectionStatus[DOIP_MAX_TESTER_CONNECTIONS];
static DoIp_ArcDoIpSocketStatusType gpConnectionStatus	= NULL;

/*
 * variable mapping from target node to last associated connection
 */
// static uint16 gpTargetConnectionMap[DOIP_TARGET_COUNT];
static uint16 *gpTargetConnectionMap = NULL;		/** modified by fly,******** **/


static uint16 gPendingRoutingActivationSocket 			= 0xff;
static uint16 gPendingRoutingActivationSa 				= 0xff;
static uint16 gPendingRoutingActivationType				= 0xffff;
static uint8* gpPendingRoutingActivationTxBuffer 		= NULL;

extern char_t 			gSoadMulticastAddr[16];			/** fly add ,******** **/
extern char_t			gSoadBroadcastAddr[16];			/** fly add ,******** **/
extern char_t			gEthInterfaceName[16];
#endif

extern soad_extcb_t gsoad_extcb;

typedef struct{
	pthread_mutex_t	MutexSend 						;
	DOIP_LINK_STATUS	LinkStatus 				;
	uint32 			DoipArcAnnouncementTimer		;
	uint16 			DoipArcAnnounceWait;

	uint8			DoipTesterCnt 					;
	uint8			DoipTargetCnt 					;
	uint8			DoipMaxTesterConnection		;
	uint8			DoipRoutingActivationCnt		;

	uint8 			VinLen							;
	uint8 * 			pVinValue					;
	uint8 			EidLen							;
	uint64 			EidValue						;
	uint8 			GidLen							;
	uint64			GidValue						;
	uint16			NumAnnouncements				;			/** add by fly,******** **/
	DoIp_ArcDoIpSocketStatusType *pConnectionStatus;
	uint16 *pTargetConnectionMap;
	uint16 PendingRoutingActivationSocket;
	uint16 PendingRoutingActivationSa;
	uint16 PendingRoutingActivationType;
	uint8* pPendingRoutingActivationTxBuffer;
	gDoIPServerDiagnosticMsg pDoIPServerDiagnosticMsg;
}SoAdDoip_cb_t;

static SoAdDoip_cb_t sgSoAdDoip_cb = {
	.MutexSend = PTHREAD_MUTEX_INITIALIZER,
	.LinkStatus = DOIP_LINK_DOWN,
	.DoipArcAnnouncementTimer = 0,
	.DoipArcAnnounceWait = 0,
	.DoipTesterCnt = DOIP_TESTER_COUNT,
	.DoipTargetCnt = DOIP_TARGET_COUNT,
	.DoipMaxTesterConnection = DOIP_MAX_TESTER_CONNECTIONS,
	.DoipRoutingActivationCnt = DOIP_ROUTINGACTIVATION_COUNT,
	.VinLen = VIN_LEN,
	.pVinValue = NULL,
	.EidLen = EID_LEN,
	.EidValue = EID,
	.GidLen = GID_LEN,
	.GidValue = GID,
	.NumAnnouncements = 0,
	.pConnectionStatus = NULL,
	.pTargetConnectionMap = NULL,
	.PendingRoutingActivationSocket = 0xff,
	.PendingRoutingActivationSa = 0xff,
	.PendingRoutingActivationType = 0xffff,
	.pPendingRoutingActivationTxBuffer = NULL,
	.pDoIPServerDiagnosticMsg = NULL
};

#if 0
static uint8 gDianostic_sduDataPtr_buffer[SOAD_RX_BUFFER_SIZE];

static PduInfoType g_dianostic_pduinfo =
{
	.SduDataPtr  = gDianostic_sduDataPtr_buffer,
	.SduLength   = 0	
};

static DianosticType gDianosticReceiveBuffer = 
{
	.dianostic_pduinfo   = &g_dianostic_pduinfo,
	.dianostic_pdutype   = 0,
	.sockNr              = 0
};
#endif

/**********************************************************
 * 函数名称:	handleTimeout
 * 函数作用:	处理超时业务
 * 函数参数:	[in] connectionIndex:		链接句柄
 * 函数返回:	无
**********************************************************/
static void handleTimeout(uint16 connectionIndex);

#ifndef DOIP_RAND
static uint16 doip_rand(void)
{
	// https://en.wikipedia.org/wiki/Xorshift
	static uint32 x = *********;
	static uint32 y = *********;
	static uint32 z = *********;
	static uint32 w = ********;
	uint32 t;

	t = x ^ (x << 11);
	x = y; y = z; z = w;
	w = w ^ (w >> 19) ^ (t ^ (t >> 8));
	
	return (uint16) w;
}
#define DOIP_RAND doip_rand
#endif

/**********************************************************
 * 函数名称:	discardIpMessage
 * 函数作用:	丢弃DoIP报文
 * 函数参数:	[in] socketHandle:		socket文件描述符
 * 函数参数:	[in] len:				丢弃的数据长度
 * 函数参数:	[in] rxBuffer:			数据缓冲区
 * 函数返回:	无
**********************************************************/
static void discardIpMessage(uint16 socketHandle, uint16 len, uint8 *rxBuffer)
{
	// Discarding this message
	while (len > SOAD_RX_BUFFER_SIZE)
	{
		recv(socketHandle, rxBuffer, SOAD_RX_BUFFER_SIZE, 0);
		len -= SOAD_RX_BUFFER_SIZE;
	}

	if (len > 0)
	{
		recv(socketHandle, rxBuffer, len, 0);
	}
}

/**********************************************************
 * 函数名称:	bincmp
 * 函数作用:	数组数据比对
 * 函数参数:	[in] arr1:		数组1
 * 函数参数:	[in] arr2:		数组2
 * 函数参数:	[in] len:		比对的长度
 * 函数返回:	相同返回0, 不同返回非0
**********************************************************/
static uint8 bincmp(const uint8* arr1, const uint8* arr2, uint32 len)
{
	uint32 i = 0;
	
	while (i < len)
	{
		uint8 res = *arr1 - *arr2;
		if (0 != res)
		{
			// non-matching!
			return res;
		}
		arr1++;
		arr2++;
		i++;
	}
	
	return 0;
}

/**********************************************************
 * 函数名称:	DoIP_LocalIpAddrAssignmentChg
 * 函数作用:	本地IP地址分配变化
 * 函数参数:	[in] linkId:		索引
 * 函数参数:	[in] state:		IP地址状态类型
 * 函数返回:	无
**********************************************************/
void DoIP_LocalIpAddrAssignmentChg(SoAd_SoConIdType linkId, TcpIp_IpAddrStateType state)
{
	switch (state)
	{
		case TCPIP_IPADDR_STATE_ASSIGNED:
		{
			sgSoAdDoip_cb.LinkStatus = DOIP_LINK_UP;
			sgSoAdDoip_cb.DoipArcAnnouncementTimer = 0;
			sgSoAdDoip_cb.DoipArcAnnounceWait = DOIP_RAND() % SOAD_DOIP_ANNOUNCE_WAIT;
			break;
		}
		default:
		{
			sgSoAdDoip_cb.LinkStatus = DOIP_LINK_DOWN;
			sgSoAdDoip_cb.DoipArcAnnouncementTimer = 0xffff;
			break;
		}
	}
}

/**********************************************************
 * 函数名称:	DoIP_createAndSendNack
 * 函数作用:	发送DoIP报文头部NACK
 * 函数参数:	[in] sockNr:		索引
 * 函数参数:	[in] nackCode:		否定响应码
 * 函数返回:	无
**********************************************************/
void DoIP_createAndSendNack(uint16 sockNr, uint8 nackCode)
{
	uint16 bytesSent;
	uint8 txBuffer[8 + 1];

	txBuffer[0] = DOIP_PROTOCOL_VERSION;
	txBuffer[1] = ~DOIP_PROTOCOL_VERSION;
	txBuffer[2] = 0x00;							// 0x0000->Negative Acknowledge
	txBuffer[3] = 0x00;
	txBuffer[4] = 0;							// Length = 0x00000001
	txBuffer[5] = 0;
	txBuffer[6] = 0;
	txBuffer[7] = 1;

	txBuffer[8] = nackCode;

	bytesSent = SoAd_SendIpMessage(sockNr, 8 + 1, txBuffer, 0);
	if (bytesSent != 8 + 1)
	{
		// Failed to send ack. Link is probably down.
		//FK_TRACE_ERROR("sockNr: %d, SOAD_DOIP_CREATE_AND_SEND_NACK_ID, SOAD_E_UNEXPECTED_EXECUTION\r\n", sockNr);
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"sockNr: %d, SOAD_DOIP_CREATE_AND_SEND_NACK_ID, SOAD_E_UNEXPECTED_EXECUTION\r\n", sockNr);
	}
}

/**********************************************************
 * 函数名称:	createAndSendDiagnosticAck
 * 函数作用:	发送诊断报文ACK确认
 * 函数参数:	[in] sockNr:		索引
 * 函数参数:	[in] sa:			源地址
 * 函数参数:	[in] ta:			目标地址
 * 函数返回:	无
**********************************************************/
static void createAndSendDiagnosticAck(uint16 sockNr, uint16 sa, uint16 ta)
{
	uint16 bytesSent;
	uint8 txBuffer[8 + 5] = {0};
	
	txBuffer[0] = DOIP_PROTOCOL_VERSION;
	txBuffer[1] = ~DOIP_PROTOCOL_VERSION;
	txBuffer[2] = 0x80;							// 0x8002->Diagnostic Message Positive Acknowledge
	txBuffer[3] = 0x02;
	txBuffer[4] = 0;							// Length = 0x00000005
	txBuffer[5] = 0;
	txBuffer[6] = 0;
	txBuffer[7] = 5;

	txBuffer[8 + 0] = ta >> 8;					// ta
	txBuffer[8 + 1] = ta;

	txBuffer[8 + 2] = sa >> 8;					// sa
	txBuffer[8 + 3] = sa;

	txBuffer[8 + 4] = 0;						// ACK

	bytesSent = SoAd_SendIpMessage(sockNr, 8 + 5, txBuffer, 0);
	if (bytesSent != 8 + 5)
	{
		// Failed to send ack. Link is probably down.
		//FK_TRACE_ERROR("sockNr: %d, SOAD_DOIP_CREATE_AND_SEND_D_ACK_ID, SOAD_E_UNEXPECTED_EXECUTION\r\n", sockNr);
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"sockNr: %d, SOAD_DOIP_CREATE_AND_SEND_D_ACK_ID, SOAD_E_UNEXPECTED_EXECUTION\n", sockNr);
	}
}

/**********************************************************
 * 函数名称:	createAndSendDiagnosticNack
 * 函数作用:	发送诊断报文NACK确认
 * 函数参数:	[in] sockNr:		索引
 * 函数参数:	[in] sa:			源地址
 * 函数参数:	[in] ta:			目标地址
 * 函数参数:	[in] nackCode:		否定响应码
 * 函数返回:	无
**********************************************************/
static void createAndSendDiagnosticNack(uint16 sockNr, uint16 sa, uint16 ta, uint8 nackCode)
{
	uint16 bytesSent;
	uint8 txBuffer[8 + 5];

	txBuffer[0] = DOIP_PROTOCOL_VERSION;
	txBuffer[1] = ~DOIP_PROTOCOL_VERSION;
	txBuffer[2] = 0x80;							// 0x8003->Diagnostic Message Negative Acknowledge
	txBuffer[3] = 0x03;
	txBuffer[4] = 0;							// Length = 0x00000005
	txBuffer[5] = 0;
	txBuffer[6] = 0;
	txBuffer[7] = 5;

	txBuffer[8 + 0] = ta >> 8;					// TA    Contains the logical address of the sender
	txBuffer[8 + 1] = ta;
 
	txBuffer[8 + 2] = sa >> 8;					// SA    Contains the logical address of the receiver
	txBuffer[8 + 3] = sa;

	txBuffer[8 + 4] = nackCode;

	bytesSent = SoAd_SendIpMessage(sockNr, 8 + 5, txBuffer, 0);
	if (bytesSent != 8 + 5)
	{
		// Failed to send ack. Link is probably down.
		//FK_TRACE_ERROR("sockNr: %d, SOAD_DOIP_CREATE_AND_SEND_D_NACK_ID, SOAD_E_UNEXPECTED_EXECUTION\r\n", sockNr);
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"sockNr: %d, SOAD_DOIP_CREATE_AND_SEND_D_NACK_ID, SOAD_E_UNEXPECTED_EXECUTION\r\n", sockNr);
	}
}

/**********************************************************
 * 函数名称:	DoIP_GetVin
 * 函数作用:	获取VIN码
 * 函数参数:	[out] buf:		数据缓冲区
 * 函数参数:	[in]  len:		缓冲区长度
 * 函数返回:	见 Std_ReturnType
**********************************************************/
Std_ReturnType DoIP_GetVin(uint8 *buf, uint8 len)
{
	if ((buf == NULL) || (len != sgSoAdDoip_cb.VinLen) || (sgSoAdDoip_cb.pVinValue == NULL))
	{
		return E_NOT_OK;
	}
	
	memcpy(buf, sgSoAdDoip_cb.pVinValue, len);
	
	return E_OK;
}

/**********************************************************
 * 函数名称:	DoIP_GetEid
 * 函数作用:	获取EID
 * 函数参数:	[out] buf:		数据缓冲区
 * 函数参数:	[in]  len:		缓冲区长度
 * 函数返回:	见 Std_ReturnType
**********************************************************/
Std_ReturnType DoIP_GetEid(uint8 *buf, uint8 len)
{
	if (buf == NULL || len != sgSoAdDoip_cb.EidLen)
	{
		return E_NOT_OK;
	}

	*buf++ = sgSoAdDoip_cb.EidValue >> 40;
	*buf++ = sgSoAdDoip_cb.EidValue >> 32;
	*buf++ = sgSoAdDoip_cb.EidValue >> 24;
	*buf++ = sgSoAdDoip_cb.EidValue >> 16;
	*buf++ = sgSoAdDoip_cb.EidValue >> 8;
	*buf   = sgSoAdDoip_cb.EidValue;
	
	return E_OK;
}

/**********************************************************
 * 函数名称:	DoIP_GetGid
 * 函数作用:	获取GID
 * 函数参数:	[out] buf:		数据缓冲区
 * 函数参数:	[in]  len:		缓冲区长度
 * 函数返回:	见 Std_ReturnType
**********************************************************/
Std_ReturnType DoIP_GetGid(uint8 *buf, uint8 len)
{
	if (buf == NULL || len != sgSoAdDoip_cb.GidLen)
	{
		return E_NOT_OK;
	}

	*buf++ = sgSoAdDoip_cb.GidValue >> 40;
	*buf++ = sgSoAdDoip_cb.GidValue >> 32;
	*buf++ = sgSoAdDoip_cb.GidValue >> 24;
	*buf++ = sgSoAdDoip_cb.GidValue >> 16;
	*buf++ = sgSoAdDoip_cb.GidValue >> 8;
	*buf   = sgSoAdDoip_cb.GidValue;

	return E_OK;
}

/**********************************************************
 * 函数名称:	DoIP_GetFurtherActionRequired
 * 函数作用:	填充将来动作的预留字段信息
 * 函数参数:	[out] buf:		数据缓冲区
 * 函数返回:	成功返回0，失败返回-1
**********************************************************/
Std_ReturnType DoIP_GetFurtherActionRequired(uint8 *buf)
{
	*buf = 0x00;		// it is ready to accept route activation from external test equiqment
	
	return E_OK;
}

/**********************************************************
 * 函数名称:	createVehicleIdentificationResponse
 * 函数作用:	填充车辆声明/车辆识别应答报文
 * 函数参数:	[out] txBuffer:		数据缓冲区
 * 函数返回:	返回报文长度
**********************************************************/
static int createVehicleIdentificationResponse(uint8* txBuffer)
{
	int i;
	int index = 0;

	txBuffer[index++] = DOIP_PROTOCOL_VERSION;
	txBuffer[index++] = ~DOIP_PROTOCOL_VERSION;
	txBuffer[index++] = 0x00;							// 0x0004->Vehicle Identification Response
	txBuffer[index++] = 0x04;
	txBuffer[index++] = 0x00;							// 0x00000020->payload lenth
	txBuffer[index++] = 0x00;
	txBuffer[index++] = 0x00;
	txBuffer[index++] = 0x20;

	// VIN field
	if (E_NOT_OK == DoIP_GetVin(&txBuffer[index], VIN_LEN))
	{
		for (i = 0; i < VIN_LEN; i++)
		{
			txBuffer[index + i] = 0;
		}
	}
	index += VIN_LEN;

	// Logical address field
	txBuffer[index + 0] = (gSoAdConfig.DoIpNodeLogicalAddress >> 8) & 0xff;
	txBuffer[index + 1] = (gSoAdConfig.DoIpNodeLogicalAddress >> 0) & 0xff;
	index += 2;

	// EID field, ethernet mac address
	if (E_NOT_OK == DoIP_GetEid(&txBuffer[index], EID_LEN))
	{   
		for (i = 0; i < EID_LEN; i++)
		{
			txBuffer[index + i] = 0;
		}
	}
	index += EID_LEN;

	// GID field
	if (E_NOT_OK == DoIP_GetGid(&txBuffer[index], GID_LEN))
	{
		for (i = 0; i < GID_LEN; i++)
		{
			txBuffer[index + i] = 0;
		}
	}
	index += GID_LEN;

	// Further action required field
	if (E_NOT_OK == DoIP_GetFurtherActionRequired(&txBuffer[index]))
	{
		txBuffer[index] = 0x00;	// No further action required
	}
	index++;

#if 0	/* 苇渡不需要支持该字段，故注释 */
	// VIN/GID Sync Status field
	if (E_NOT_OK == DoIP_GetFurtherActionRequired(&txBuffer[index]))
	{
		uint8 VinGidSyncStatus = 0x10;
		txBuffer[index] = VinGidSyncStatus;
	}
	index++;
#endif

	return index;
}

/**********************************************************
 * 函数名称:	DoIP_SendVehicleAnnouncement
 * 函数作用:	发送车辆声明报文
 * 函数参数:	[in] sockNr:			索引
 * 函数返回:	无
**********************************************************/
void DoIP_SendVehicleAnnouncement(uint16 sockNr)
{
	uint16 sendLen;
	uint8* txBuffer;
	uint16 bytesSent;

	if (SoAd_BufferGet(64, &txBuffer))
	{
		sendLen = createVehicleIdentificationResponse(txBuffer);

		bytesSent = SoAd_SendIpMessage(sockNr, sendLen, txBuffer, 1);
		if (bytesSent != sendLen)
		{
			// Failed to send ack. Link is probably down.
			//FK_TRACE_ERROR("DoIP_SendVehicleAnnouncement, bytesSent: %d, sendLen: %d\r\n", bytesSent, sendLen);
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"DoIP_SendVehicleAnnouncement, bytesSent: %d, sendLen: %d\r\n", bytesSent, sendLen);
		}
		
		SoAd_BufferFree(txBuffer);
	}
	else
	{
		// Not enough buffers. Report error in error log.
		//FK_TRACE_ERROR("SOAD_DOIP_HANDLE_VEHICLE_ID_REQ_ID, SOAD_E_NOBUFS\r\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_HANDLE_VEHICLE_ID_REQ_ID, SOAD_E_NOBUFS\r\n");
	}
}

/**********************************************************
 * 函数名称:	handleVehicleIdentificationReq
 * 函数作用:	负载类型为0x0001-0x0003的车辆识别请求处理
 * 函数参数:	[in] sockNr:			索引
 * 函数参数:	[in] payloadLength:		负载长度
 * 函数参数:	[in] rxBuffer:			负载数据
 * 函数参数:	[in] type:				车辆识别请求类型
 * 函数返回:	无
**********************************************************/
static void handleVehicleIdentificationReq(uint16 sockNr, uint32 payloadLength, \
		uint8 *rxBuffer, SoAd_Arc_DoIp_VehicleIdentificationRequestType type)
{
	uint16 sendLen;
	uint8* txBuffer;
	uint16 bytesSent;
	boolean doRespond = FALSE;

	if (SOAD_ARC_DOIP_IDENTIFICATIONREQUEST_ALL == type)
	{
		if (0 == payloadLength)
		{
			doRespond = TRUE;
		}
		else
		{
			// Invalid payload length!
			//FK_TRACE_ERROR("SoAd_SocketClose\r\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SoAd_SocketClose\r\n");
			DoIP_createAndSendNack(sockNr, DOIP_E_INVALID_PAYLOAD_LENGTH);
			SoAd_SocketClose(sockNr);
		}
	}
	else if (SOAD_ARC_DOIP_IDENTIFICATIONREQUEST_BY_EID == type)
	{
		if (EID_LEN == payloadLength)
		{
			uint8 myEid[EID_LEN];
			DoIP_GetEid(myEid, sizeof(myEid));

			if (0 == bincmp(myEid, &rxBuffer[8], sizeof(myEid)))
			{
				doRespond = TRUE;
			}
		}
		else
		{
			// Invalid payload length!
			//FK_TRACE_ERROR("SoAd_SocketClose\r\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SoAd_SocketClose\n");
			DoIP_createAndSendNack(sockNr, DOIP_E_INVALID_PAYLOAD_LENGTH);
			SoAd_SocketClose(sockNr);
		}
	}
	else if (SOAD_ARC_DOIP_IDENTIFICATIONREQUEST_BY_VIN == type)
	{
		if (VIN_LEN == payloadLength)
		{
			uint8 myVin[VIN_LEN];
			DoIP_GetVin(myVin, sizeof(myVin));

			if (0 == bincmp(myVin, &rxBuffer[8], sizeof(myVin)))
			{
				doRespond = TRUE;
			}
			else
			{
				//FK_TRACE_INFO("myVin: \n");
				//FK_TRACE_INFO_HEX(myVin, VIN_LEN);
				//FK_TRACE_INFO("rec_vin: \n");
				//FK_TRACE_INFO_HEX(&rxBuffer[8], VIN_LEN);
				//FK_TRACE_INFO("bincmp fail\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"bincmp fail\n");
			}
		}
		else
		{
			// Invalid payload length!
			//FK_TRACE_ERROR("SoAd_SocketClose \n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SoAd_SocketClose \n");
			DoIP_createAndSendNack(sockNr, DOIP_E_INVALID_PAYLOAD_LENGTH);
			SoAd_SocketClose(sockNr);
		}
	}
	else
	{
		DoIP_createAndSendNack(sockNr, DOIP_E_UNKNOWN_PAYLOAD_TYPE);
	}

	if (doRespond)
	{
		if (SoAd_BufferGet(64, &txBuffer))
		{
			sendLen = createVehicleIdentificationResponse(txBuffer);

			bytesSent = SoAd_SendIpMessage(sockNr, sendLen, txBuffer, 0);
			if (bytesSent != sendLen)
			{
				// Failed to send data. Link is probably down.
				//FK_TRACE_ERROR("SOAD_DOIP_HANDLE_VEHICLE_ID_REQ_ID, SOAD_E_UNEXPECTED_EXECUTION\r\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_HANDLE_VEHICLE_ID_REQ_ID, SOAD_E_UNEXPECTED_EXECUTION\r\n");
			}
			SoAd_BufferFree(txBuffer);
		}
		else
		{
			// Not enough tx buffer available. Report a Det error?
			//FK_TRACE_ERROR("SOAD_DOIP_HANDLE_VEHICLE_ID_REQ_ID, SOAD_E_NOBUFS\r\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_HANDLE_VEHICLE_ID_REQ_ID, SOAD_E_NOBUFS\r\n");
			DoIP_createAndSendNack(sockNr, DOIP_E_OUT_OF_MEMORY);
		}
	}
}

/**********************************************************
 * 函数名称:	isSourceAddressKnown
 * 函数作用:	判断诊断仪逻辑地址是否支持
 * 函数参数:	[in] sa:	源地址
 * 函数返回:	支持返回TRUE, 不支持返回FALSE
**********************************************************/
static boolean isSourceAddressKnown(uint16 sa)
{
	uint16 i;
	boolean saKnown = FALSE;

	// for (i = 0; (i < DOIP_TESTER_COUNT) && (FALSE == saKnown); i++)		/** modified by fly,******** **/
	for (i = 0; i < sgSoAdDoip_cb.DoipTesterCnt; i++)
	{
		//FK_TRACE_INFO("gSoAdConfig.DoIpTesters[%d].address: 0x%04X, sa: 0x%04X\n", i, gSoAdConfig.DoIpTesters[i].address, sa);
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"gSoAdConfig.DoIpTesters[%d].address: 0x%04X, sa: 0x%04X\n", i, gSoAdConfig.DoIpTesters[i].address, sa);
		if (gSoAdConfig.DoIpTesters[i].address == sa)
		{
			saKnown = TRUE;
			break;
		}
	}

	return saKnown;
}

/**********************************************************
 * 函数名称:	isRoutingTypeSupported
 * 函数作用:	判断路由激活类型是否支持
 * 函数参数:	[in] activationType:	路由激活类型
 * 函数返回:	支持返回TRUE, 不支持返回FALSE
**********************************************************/
static boolean isRoutingTypeSupported(uint16 activationType)
{
	uint16 i;
	boolean supported = FALSE;
	#if 0
	FK_TRACE_INFO("DOIP_ROUTINGACTIVATION_COUNT: %d, DoIpRoutingActivations: %d, activationType: 0x%02X\n", \
			DOIP_ROUTINGACTIVATION_COUNT, sgSoAdDoip_cb.DoipRoutingActivationCnt, activationType);
	#endif
	SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"DOIP_ROUTINGACTIVATION_COUNT: %d, DoIpRoutingActivations: %d, activationType: 0x%02X\n", \
			DOIP_ROUTINGACTIVATION_COUNT, sgSoAdDoip_cb.DoipRoutingActivationCnt, activationType);
	// for (i = 0; (i < DOIP_ROUTINGACTIVATION_COUNT) && (FALSE == supported); i++)		/** modified by fly,******** **/
	for (i = 0; i < sgSoAdDoip_cb.DoipRoutingActivationCnt; i++)
	{
		//SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"activationNumber: %d, activationType: 0x%02X\n",gSoAdConfig.DoIpRoutingActivations[i].activationNumber, activationType);
		if (gSoAdConfig.DoIpRoutingActivations[i].activationNumber == activationType)
		{
			supported = TRUE;
			break;
		}
	}

	return supported;
}

/**********************************************************
 * 函数名称:	isAuthenticationRequired
 * 函数作用:	需要认证请求
 * 函数参数:	[in] activationType:	激活类型
 * 函数返回:	成功返回true,失败返回False
**********************************************************/
static boolean isAuthenticationRequired(uint16 activationType)
{
	uint16 i;
	boolean req = FALSE;

	// for (i = 0; (i < DOIP_ROUTINGACTIVATION_COUNT) && (FALSE == req); i++)		/** modified by fly,******** **/
	for (i = 0; (i < sgSoAdDoip_cb.DoipRoutingActivationCnt) && (FALSE == req); i++)
	{
		if (gSoAdConfig.DoIpRoutingActivations[i].activationNumber == activationType)
		{
			if (NULL == gSoAdConfig.DoIpRoutingActivations[i].authenticationCallback)
			{
				req = FALSE;
			}
			else
			{
				req = TRUE;
			}
			
			return req;
		}
	}

	return req;
}

/**********************************************************
 * 函数名称:	isAuthenticated
 * 函数作用:	通过身份确认
 * 函数参数:	[in] activationType:	激活类型
 * 函数返回:	成功返回true,失败返回False
**********************************************************/
static boolean isAuthenticated(uint16 activationType)
{
	// TODO: Authentication not supported
	return FALSE;
}

/**********************************************************
 * 函数名称:	isConfirmationRequired
 * 函数作用:	需要认证请求
 * 函数参数:	[in] activationType:	激活类型
 * 函数返回:	成功返回true,失败返回False
**********************************************************/
static boolean isConfirmationRequired(uint16 activationType)
{
	uint16 i;
	boolean req = FALSE;

	// for (i = 0; (i < DOIP_ROUTINGACTIVATION_COUNT) && (FALSE == req); i++)		/** modified by fly,******** **/
	for (i = 0; (i < sgSoAdDoip_cb.DoipRoutingActivationCnt) && (FALSE == req); i++)
	{
		if (gSoAdConfig.DoIpRoutingActivations[i].activationNumber == activationType)
		{
			if (NULL == gSoAdConfig.DoIpRoutingActivations[i].confirmationCallback)
			{
				req = FALSE;
			}
			else
			{
				req = TRUE;
			}
			
			return req;
		}
	}

	return req;
}

/**********************************************************
 * 函数名称:	isConfirmed
 * 函数作用:	通过认证
 * 函数参数:	[in] activationType:	激活类型
 * 函数返回:	成功返回true,失败返回False
**********************************************************/
static boolean isConfirmed(uint16 activationType)
{
	// TODO: Confirmation not supported
	return FALSE;
}

/**********************************************************
 * 函数名称:	createAndSendAliveCheck
 * 函数作用:	发送在线检查请求
 * 函数参数:	[in] connectionIndex:	索引
 * 函数返回:	无
**********************************************************/
static void createAndSendAliveCheck(uint16 connectionIndex)
{
	uint8 *txBuffer;
	uint16 bytesSent;

	if (SoAd_BufferGet(8, &txBuffer))
	{
		txBuffer[0] = DOIP_PROTOCOL_VERSION;
		txBuffer[1] = ~DOIP_PROTOCOL_VERSION;
		txBuffer[2] = 0x00;							// 0x0007->Alive check request
		txBuffer[3] = 0x07;
		txBuffer[4] = 0;							// Length = 0x0000000 (0)
		txBuffer[5] = 0;
		txBuffer[6] = 0;
		txBuffer[7] = 0;

		bytesSent = SoAd_SendIpMessage(sgSoAdDoip_cb.pConnectionStatus[connectionIndex].sockNr, 8, txBuffer, 0);
		if (bytesSent != 8)
		{
			/*
			 * Could not send data - socket probably broken, so let's close the socket.
			 */
			sgSoAdDoip_cb.pConnectionStatus[connectionIndex].socketState = DOIP_ARC_CONNECTION_INVALID;

			/* If there are pending routing activations waiting, activate that one. */
			if (0xff != sgSoAdDoip_cb.PendingRoutingActivationSocket)
			{
				handleTimeout(connectionIndex);
			}
			else
			{
				//FK_TRACE_ERROR("SoAd_SocketClose\r\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SoAd_SocketClose\r\n");
				SoAd_SocketClose(sgSoAdDoip_cb.pConnectionStatus[connectionIndex].sockNr);
			}
		}
		SoAd_BufferFree(txBuffer);
	}
	else
	{
		// No tx buffer available. Report a Det error.
		//FK_TRACE_ERROR("SOAD_DOIP_CREATE_AND_SEND_ALIVE_CHECK_ID, SOAD_E_NOBUFS\r\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_CREATE_AND_SEND_ALIVE_CHECK_ID, SOAD_E_NOBUFS\r\n");
	}
}

/**********************************************************
 * 函数名称:	startSingleSaAliveCheck
 * 函数作用:	发起对指定逻辑地址的套接字在线检测请求
 * 函数参数:	[in] sa:	源地址
 * 函数返回:	无
**********************************************************/
static void startSingleSaAliveCheck(uint16 sa)
{
	uint16 i;
	
	// for (i = 0; i < DOIP_MAX_TESTER_CONNECTIONS; i++)		/** modified by fly,******** **/
	for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++)
	{
		if ((sgSoAdDoip_cb.pConnectionStatus[i].sa == sa) && (FALSE == sgSoAdDoip_cb.pConnectionStatus[i].awaitingAliveCheckResponse))
		{
			sgSoAdDoip_cb.pConnectionStatus[i].awaitingAliveCheckResponse = TRUE;
			sgSoAdDoip_cb.pConnectionStatus[i].generalInactivityTimer = 0;
			sgSoAdDoip_cb.pConnectionStatus[i].aliveCheckTimer = 0;
			createAndSendAliveCheck(i);
		}
	}
}

/**********************************************************
 * 函数名称:	startAllSocketsAliveCheck
 * 函数作用:	发起对所有套接字的在线检测请求
 * 函数参数:	无
 * 函数返回:	无
**********************************************************/
static void startAllSocketsAliveCheck(void)
{
	uint16 i;
	
	// for (i = 0; i < DOIP_MAX_TESTER_CONNECTIONS; i++)			/** modified by fly,******** **/
	for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++)
	{ 
		// No need to check connection states as this method is only called
		// when all sockets are in registered state...

		sgSoAdDoip_cb.pConnectionStatus[i].awaitingAliveCheckResponse = TRUE;
		sgSoAdDoip_cb.pConnectionStatus[i].generalInactivityTimer = 0;
		sgSoAdDoip_cb.pConnectionStatus[i].aliveCheckTimer = 0;
		createAndSendAliveCheck(i);
	}
}

/**********************************************************
 * 函数名称:	registerSocket
 * 函数作用:	注册套接字
 * 函数参数:	[in] slotIndex:			索引
 * 函数参数:	[in] sockNr:			索引
 * 函数参数:	[in] activationType:	路由激活类型
 * 函数参数:	[in] sa:				源地址
 * 函数返回:	无
**********************************************************/
static void registerSocket(uint16 slotIndex, uint16 sockNr, uint16 activationType, uint16 sa)
{
	assert(slotIndex < sgSoAdDoip_cb.DoipMaxTesterConnection);

	sgSoAdDoip_cb.pConnectionStatus[slotIndex].sockNr         = sockNr;
	sgSoAdDoip_cb.pConnectionStatus[slotIndex].sa             = sa;
	sgSoAdDoip_cb.pConnectionStatus[slotIndex].activationType = activationType;

	sgSoAdDoip_cb.pConnectionStatus[slotIndex].generalInactivityTimer = 0;
	sgSoAdDoip_cb.pConnectionStatus[slotIndex].initialInactivityTimer = 0;

	sgSoAdDoip_cb.pConnectionStatus[slotIndex].authenticated = FALSE;
	sgSoAdDoip_cb.pConnectionStatus[slotIndex].confirmed     = FALSE;

	sgSoAdDoip_cb.pConnectionStatus[slotIndex].awaitingAliveCheckResponse = FALSE;

	sgSoAdDoip_cb.pConnectionStatus[slotIndex].socketState = DOIP_ARC_CONNECTION_REGISTERED;
}

/**********************************************************
 * 函数名称:	socketHandle
 * 函数作用:	套接字处理
 * 函数参数:	[in]  sockNr:							索引
 * 函数参数:	[in]  activationType:					路由激活类型
 * 函数参数:	[in]  sa:								源地址
 * 函数参数:	[out] routingActivationResponseCode:	路由激活响应码
 * 函数返回:	见 DoIp_Arc_SocketAssignmentResultType
**********************************************************/
static DoIp_Arc_SocketAssignmentResultType socketHandle(uint16 sockNr, uint16 activationType, uint16 sa, uint8* routingActivationResponseCode)
{
	/*
	 * This method is intended to implement Figure 13 in ISO/FDIS 13400-2:2012(E)
	 */
	uint16 i;
	uint16 numRegisteredSockets = 0;

	for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++)
	{
		if (DOIP_ARC_CONNECTION_REGISTERED == sgSoAdDoip_cb.pConnectionStatus[i].socketState)
		{
			numRegisteredSockets++;
		}
	}

	if (0 == numRegisteredSockets)
	{
		// No registered sockets, so we pick a slot for this connection:
		registerSocket(0, sockNr, activationType, sa);

		// No need to set *routingActivationResponseCode when socket assignment is successful.
		// *routingActivationResponseCode = 0;
		return DOIP_SOCKET_ASSIGNMENT_SUCCESSFUL;
	}
	else
	{
		// There is at least one registered socket already. Let's find out if it's this socket..
		for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++)
		{
			if (sgSoAdDoip_cb.pConnectionStatus[i].sockNr == sockNr)
			{
				// We found the TCP socket. Is it registered?
				if (sgSoAdDoip_cb.pConnectionStatus[i].socketState == DOIP_ARC_CONNECTION_REGISTERED)
				{
					// We found the registered TCP socket. Is it assigned to this tester (SA)?
					if (sgSoAdDoip_cb.pConnectionStatus[i].sa == sa)
					{
						/* @req DoIP-080 */
						sgSoAdDoip_cb.pConnectionStatus[i].generalInactivityTimer = 0;

						/* @req DoIP-089 */
						return DOIP_SOCKET_ASSIGNMENT_SUCCESSFUL;
					}
					else
					{
						/* @req DoIP-106 */
						/* Routing activation denied because an SA different
						 * from the table connection entry was received on the
						 * already activated TCP_DATA socket. */
						*routingActivationResponseCode = ROUTING_ACTIVATION_SA_DIFFERENT;
						return DOIP_SOCKET_ASSIGNMENT_FAILED;
					}
				}
			}
		}

		/*
		 *  The For loop above terminated; that means that the current socket is currently not registered.
		 *
		 *  Next up: Check if SA is already registered to another socket...
		 */
		for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++)
		{
			if ((sgSoAdDoip_cb.pConnectionStatus[i].sa == sa) && (DOIP_ARC_CONNECTION_REGISTERED == sgSoAdDoip_cb.pConnectionStatus[i].socketState))
			{
				// Yes, the SA is already registered on another socket!
				/* @req DoIP-091 */
				// perform alive check single SA
				startSingleSaAliveCheck(sa);
				*routingActivationResponseCode = ROUTING_ACTIVATION_DUP_REGISTERED;
				//FK_TRACE_INFO("startSingleSaAliveCheck i: %d\n", i) ;
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"startSingleSaAliveCheck i: %d\n", i) ;
			
				return DOIP_SOCKET_ASSIGNMENT_PENDING;
			}
		}

		/*
		 * The For loop above terminated; that means that the current SA is not already registered to another socket.
		 *
		 * Next up: Check to see that there is a free socket slot available..
		 */
		for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++)
		{
			if (DOIP_ARC_CONNECTION_INVALID == sgSoAdDoip_cb.pConnectionStatus[i].socketState)
			{
				/* Yes, we found a free slot */
				/* @req DoIp-090 */
				registerSocket(i, sockNr, activationType, sa);
				
				return DOIP_SOCKET_ASSIGNMENT_SUCCESSFUL;
			}
		}

		/*
		 * The For loop above terminated; that means that there are no free slots.
		 *
		 * Perform alive check on all registered sockets...
		 */
		/* @req DoIP-094 */
		startAllSocketsAliveCheck();
		
		return DOIP_SOCKET_ASSIGNMENT_PENDING;
	}
}

/**********************************************************
 * 函数名称:	fillRoutingActivationResponseData
 * 函数作用:	填充路由激活响应报文
 * 函数参数:	[in] txBuffer:							报文数据
 * 函数参数:	[in] sa:								源地址
 * 函数参数:	[in] routingActivationResponseCode:		路由激活响应码
 * 函数返回:	返回报文长度
**********************************************************/
static uint16 fillRoutingActivationResponseData(uint8 *txBuffer, uint16 sa, uint8 routingActivationResponseCode)
{
	uint16 index = 0;

	txBuffer[index++] = DOIP_PROTOCOL_VERSION;
	txBuffer[index++] = ~DOIP_PROTOCOL_VERSION;
	txBuffer[index++] = 0x00; 						// 0x0006->Routing Activation Response
	txBuffer[index++] = 0x06;
	txBuffer[index++] = 0x00;						// Length = 0x00000009
	txBuffer[index++] = 0x00;
	txBuffer[index++] = 0x00;
	txBuffer[index++] = 0x09;
	
	txBuffer[index++] = sa >> 8;
	txBuffer[index++] = sa;
	txBuffer[index++] = (gSoAdConfig.DoIpNodeLogicalAddress >> 8) & 0xff;
	txBuffer[index++] = (gSoAdConfig.DoIpNodeLogicalAddress >> 0) & 0xff;
	txBuffer[index++] = routingActivationResponseCode;
	txBuffer[index++] = 0; 							// Reserved
	txBuffer[index++] = 0;
	txBuffer[index++] = 0;
	txBuffer[index++] = 0;
#if 0		/* 苇渡科技不需要该字段，故注释 */
	txBuffer[index++] = 0; 							// OEM use
	txBuffer[index++] = 0;
	txBuffer[index++] = 0;
	txBuffer[index++] = 0;
#endif
	
	return index;
}

/**********************************************************
 * 函数名称:	fillAliveCheckResponseData
 * 函数作用:	填充在线检查应答报文
 * 函数参数:	[in] txBuffer:		报文数据
 * 函数参数:	[in] sa:			源地址
 * 函数返回:	无
**********************************************************/
static void fillAliveCheckResponseData(uint8 *txBuffer, uint16 sa)
{
	txBuffer[0] = DOIP_PROTOCOL_VERSION;
	txBuffer[1] = ~DOIP_PROTOCOL_VERSION;
	txBuffer[2] = 0x00; 						// 0x0006->Routing Activation Response
	txBuffer[3] = 0x08;
	txBuffer[4] = 0; 							// Length = 0x00000002
	txBuffer[5] = 0;
	txBuffer[6] = 0;
	txBuffer[7] = 2;
	txBuffer[8] = sa >> 8;
	txBuffer[8 + 1] = sa;
}

/**********************************************************
 * 函数名称:	handleRoutingActivationReq
 * 函数作用:	负载类型为0x0005的路由激活请求处理
 * 函数参数:	[in] sockNr:			索引
 * 函数参数:	[in] payloadLength:		负载长度
 * 函数参数:	[in] rxBuffer:			负载数据
 * 函数返回:	无
**********************************************************/
static void handleRoutingActivationReq(uint16 sockNr, uint32 payloadLength, uint8 *rxBuffer)
{
	uint16 sa;
	uint8 *txBuffer;
	uint16 bytesSent;
	uint16 activationType;

	/** @req SWS_DoIP_00117  is invalid (refers to the wrong service and does not match
	 * ISO13400-2:2012 issued on 2012-06-18)
	 *
	 * Decision made together with Customer's diagnostic tester group that activation
	 * type is only one byte, thus payload length may be 11 or 7. (Not 12 or 8 as
	 * AUTOSAR requirement states)
	 */
	
	if ((7 == payloadLength) || (11 == payloadLength) || (10 == payloadLength/*TODO REMOVE WHEN MOVED TO CANOE8.1*/))
	{
		sa = (rxBuffer[8 + 0] << 8) + rxBuffer[8 + 1];
		activationType = rxBuffer[8 + 2];
		
		if (SoAd_BufferGet(24, &txBuffer))
		{
			uint8 routingActivationResponseCode = 0x7e;			// Vehicle manufacturer-specific
			if (TRUE == isSourceAddressKnown(sa))
			{
				if (TRUE == isRoutingTypeSupported(activationType))
				{
					DoIp_Arc_SocketAssignmentResultType socketHandleResult = socketHandle(sockNr, activationType, sa, &routingActivationResponseCode);
					if (DOIP_SOCKET_ASSIGNMENT_SUCCESSFUL == socketHandleResult)
					{
						boolean authenticated;

						if (TRUE == isAuthenticationRequired(activationType))
						{
							authenticated = isAuthenticated(activationType);
						}
						else
						{
							authenticated = TRUE;
						}

						if (authenticated)
						{
							if (TRUE == isConfirmationRequired(activationType))
							{
								if (TRUE == isConfirmed(activationType))
								{
									/* Routing successfully activated */
									routingActivationResponseCode = ROUTING_ACTIVATION_SUCCESSFULLY;
								} 
								else 
								{
									/* Routing will be activated; confirmation required */
									routingActivationResponseCode = ROUTING_ACTIVATION_CONFIRM;
								}
							} 
							else 
							{
								/* Routing successfully activated */
								routingActivationResponseCode = ROUTING_ACTIVATION_SUCCESSFULLY;
							}
						}
						else 
						{
							/* Routing activation rejected due to missing authentication */
							/** @req DoIP-061 */
							routingActivationResponseCode = ROUTING_ACTIVATION_MISSING_AUT;
						}
					}
					else if (DOIP_SOCKET_ASSIGNMENT_FAILED == socketHandleResult) 
					{
						/* socketHandle */
						/* Routing activation denied because:
						 *  0x01 - all concurrently supported TCP_DATA sockets are
						 *         registered and active
						 *  0x02 - an SA different from the table connection entry
						 *         was received on the already activated TCP_DATA
						 *         socket
						 *  0x03 - the SA is already registered and active on a
						 *         different TCP_DATA socket
						 *
						 *  socketHandle() shall already have written the corresponding response code to
						 *  routingActivationResponseCode
						 */

						/* Validate response code */
						switch (routingActivationResponseCode) 
						{
							case ROUTING_ACTIVATION_NO_SOEKET_USE:
							case ROUTING_ACTIVATION_SA_DIFFERENT:
							case ROUTING_ACTIVATION_DUP_REGISTERED:
								/* OK! */
								break;
							default:
								/* Unsupported response code at this level */
								routingActivationResponseCode = 0x7e;
								//FK_TRACE_ERROR("SOAD_DOIP_ROUTING_ACTIVATION_REQ_ID, SOAD_E_SHALL_NOT_HAPPEN\r\n");
								SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_ROUTING_ACTIVATION_REQ_ID, SOAD_E_SHALL_NOT_HAPPEN\r\n");
								break;
						}
					}
					else if (DOIP_SOCKET_ASSIGNMENT_PENDING == socketHandleResult) 
					{
						/* socketHandle */
						/*
						 * Trying to assign a connection slot, but pending
						 * alive check responses before continuing.
						 * Continuation handled from DoIP_MainFunction (if a
						 * connection times out and thus becomes free) or from
						 * handleAliveCheckResp (if all connections remain
						 * active)
						 */
						if (NULL == sgSoAdDoip_cb.pPendingRoutingActivationTxBuffer) 
						{
							sgSoAdDoip_cb.pPendingRoutingActivationTxBuffer = txBuffer;
							sgSoAdDoip_cb.PendingRoutingActivationSa = sa;
							sgSoAdDoip_cb.PendingRoutingActivationSocket = sockNr;
							sgSoAdDoip_cb.PendingRoutingActivationType = activationType;
						}
						else 
						{
							// Socket assignment pending; alive check response already pending
							// This should not happen - the connection attempt should not have been accepted..
							//FK_TRACE_ERROR("SOAD_DOIP_ROUTING_ACTIVATION_REQ_ID, SOAD_E_SHALL_NOT_HAPPEN\r\n");
							SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_ROUTING_ACTIVATION_REQ_ID, SOAD_E_SHALL_NOT_HAPPEN\r\n");
						}
						return;
					}
					else 
					{
						/* This cannot happen */
						//FK_TRACE_ERROR(" SOAD_DOIP_ROUTING_ACTIVATION_REQ_ID, SOAD_E_SHALL_NOT_HAPPEN\r\n");
						SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR," SOAD_DOIP_ROUTING_ACTIVATION_REQ_ID, SOAD_E_SHALL_NOT_HAPPEN\r\n");
					}
				}
				else
				{
					/* isRoutingTypeSupported(activationType) */
					/* Routing activation denied due to unsupported routing activation type */
					/** @req DoIP-151 */
					//FK_TRACE_INFO("routingActivationResponseCode is 0x06\r\n");
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"routingActivationResponseCode is 0x06\r\n");
					routingActivationResponseCode = ROUTING_ACTIVATION_UNSUPPORT_TYPE;
				}
			}
			else 
			{
				/* Routing activation rejected due to unknown source address */
				/** @req DoIP-059 */
				routingActivationResponseCode = ROUTING_ACTIVATION_UNKNOWN_SA;
			}
			
			//FK_TRACE_INFO("fillRoutingActivationResponseData: 0x%X\r\n", routingActivationResponseCode);
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"fillRoutingActivationResponseData: 0x%X\r\n", routingActivationResponseCode);
			uint16 sendLen = fillRoutingActivationResponseData(txBuffer, sa, routingActivationResponseCode);
			bytesSent = SoAd_SendIpMessage(sockNr, sendLen, txBuffer, 0);
			if (bytesSent != sendLen)
			{
				// Could not send data - report error in development error tracer
				//FK_TRACE_ERROR(" SOAD_DOIP_ROUTING_ACTIVATION_REQ_ID, SOAD_E_UNEXPECTED_EXECUTION\r\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR," SOAD_DOIP_ROUTING_ACTIVATION_REQ_ID, SOAD_E_UNEXPECTED_EXECUTION\r\n");
			}
			else
			{
				//DoIP_Server_Event_Notifier(EVENT_ROUTING_ACTIVATION, &sa);
				if(gsoad_extcb.DoipEventNotifier != NULL){
					gsoad_extcb.DoipEventNotifier(EVENT_ROUTING_ACTIVATION, &sa);
				}
			}
			SoAd_BufferFree(txBuffer);

			/**
			 * @req DoIP-102
			 *
			 * Negative response code --> Close socket on which the current message was received
			 */
			switch (routingActivationResponseCode)
			{
				case ROUTING_ACTIVATION_SUCCESSFULLY:			// Routing activated.
				case ROUTING_ACTIVATION_CONFIRM:				// Confirmation pending
				case ROUTING_ACTIVATION_MISSING_AUT:			// Missing authentication
				{
					break;
				}
				default:
				{
					#if 0
					FK_TRACE_ERROR("SoAd_SocketClose, routingActivationResponseCode: %d\n", \
							routingActivationResponseCode);
					#endif
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SoAd_SocketClose, routingActivationResponseCode: %d\n", \
							routingActivationResponseCode);
					DoIP_PthreadMutexLock();
					SoAd_SocketClose(sockNr);
					DoIP_PthreadMutexUnlock();
					break;
				}
			}
		}
		else
		{
			//FK_TRACE_ERROR("SOAD_DOIP_ROUTING_ACTIVATION_REQ_ID, SOAD_E_NOBUFS\r\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_ROUTING_ACTIVATION_REQ_ID, SOAD_E_NOBUFS\r\n");
			DoIP_createAndSendNack(sockNr, DOIP_E_OUT_OF_MEMORY);
		}
	}
	else
	{
		//FK_TRACE_ERROR("SoAd_SocketClose \r\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SoAd_SocketClose \r\n");
		DoIP_createAndSendNack(sockNr, DOIP_E_INVALID_PAYLOAD_LENGTH);
		DoIP_PthreadMutexLock();
		SoAd_SocketClose(sockNr);
		DoIP_PthreadMutexUnlock();
	}
}

/**********************************************************
 * 函数名称:	lookupSaTa
 * 函数作用:	查询sa\ta地址是否合法
 * 函数参数:	[in] connectionIndex:			connect链接索引
 * 函数参数:	[in] sa:		源地址
 * 函数参数:	[in] ta:			目标地址
 * 函数参数:	[in/out] targetIndex:			目标地址列表索引返回
 * 函数返回:	成功返回0，失败返回其他值
**********************************************************/
static LookupSaTaResultType lookupSaTa(uint16 connectionIndex, uint16 sa, uint16 ta, uint16* targetIndex)
{
	uint16 i;
	uint16 routingActivationIndex = 0xffff;

	if (0xffff == connectionIndex)
	{
		// Connection not registered!
		return LOOKUP_SA_TA_SAERR;
	}

	//for (i = 0; i < DOIP_ROUTINGACTIVATION_COUNT; i++) 
	for (i = 0; i < sgSoAdDoip_cb.DoipRoutingActivationCnt; i++)
	{
		/* FK_TRACE_INFO("activationType: %d, %d\r\n", sgSoAdDoip_cb.pConnectionStatus[connectionIndex].activationType, \
				gSoAdConfig.DoIpRoutingActivations[i].activationNumber); */

		if (sgSoAdDoip_cb.pConnectionStatus[connectionIndex].activationType == gSoAdConfig.DoIpRoutingActivations[i].activationNumber)
		{
			routingActivationIndex = i;
			break;
		}
	}

	if (0xffff == routingActivationIndex)
	{
		// No such routing activation!
		//FK_TRACE_INFO("No such routing activation!\r\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"No such routing activation!\r\n");
		return LOOKUP_SA_TA_TAUNKNOWN;
	}

	for (i = 0; i < sgSoAdDoip_cb.DoipRoutingActivationCnt * sgSoAdDoip_cb.DoipTargetCnt; i++)
	{
		uint16 itTargetIndex = gSoAdConfig.DoIpRoutingActivationToTargetAddressMap[i].target;

		/* FK_TRACE_INFO("itTargetIndex: %d, address: [0x%X][0x%X], routingActivation: [%d][%d]\r\n", \
				itTargetIndex, \
				ta, gSoAdConfig.DoIpTargetAddresses[itTargetIndex].addressValue, \
				routingActivationIndex, gSoAdConfig.DoIpRoutingActivationToTargetAddressMap[i].routingActivation); */
		if ((ta == gSoAdConfig.DoIpTargetAddresses[itTargetIndex].addressValue) \
				&& (routingActivationIndex == gSoAdConfig.DoIpRoutingActivationToTargetAddressMap[i].routingActivation))
		{
			*targetIndex = itTargetIndex;
			return LOOKUP_SA_TA_OK;
		}
	}

	return LOOKUP_SA_TA_TAUNKNOWN;
}

/**********************************************************
 * 函数名称:	handleEntityStatusReq
 * 函数作用:	负载类型为0x4001的DoIP实体状态请求处理
 * 函数参数:	[in] sockNr:			索引
 * 函数参数:	[in] payloadLength:		负载长度
 * 函数参数:	[in] rxBuffer:			负载数据
 * 函数返回:	无
**********************************************************/
static void handleEntityStatusReq(uint16 sockNr, uint32 payloadLength, uint8 *rxBuffer)
{
	//FK_TRACE_INFO("handleEntityStatusReq payloadLength: %d\r\n", payloadLength);
	SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"handleEntityStatusReq payloadLength: %d\r\n", payloadLength);
	if (0 == payloadLength) 
	{
		uint8* txBuffer = NULL;
		if (SoAd_BufferGet(8 + 7, &txBuffer))
		{
			uint8 maxNofSockets = SoAd_GetNofMaxUsedTcpSockets();
			uint8 curNofOpenSockets = SoAd_GetNofCurrentlyUsedTcpSockets();
			uint32 maxDataSize = SOAD_RX_BUFFER_SIZE;
			uint16 bytesSent;

			txBuffer[0] = DOIP_PROTOCOL_VERSION;
			txBuffer[1] = ~DOIP_PROTOCOL_VERSION;
			txBuffer[2] = 0x40;							// 0x4002->Entity status response
			txBuffer[3] = 0x02;
			txBuffer[4] = 0;							// Length = 0x00000007 (7)
			txBuffer[5] = 0;
			txBuffer[6] = 0;
			txBuffer[7] = 7;

			txBuffer[8] = DOIP_GATEWAY;
			txBuffer[9] = maxNofSockets;				// Max TCP sockets
			txBuffer[10] = curNofOpenSockets;			// Currently open TCP sockets

			txBuffer[11] = (maxDataSize >> 24) & 0xff;
			txBuffer[12] = (maxDataSize >> 16) & 0xff;
			txBuffer[13] = (maxDataSize >> 8) & 0xff;
			txBuffer[14] = maxDataSize & 0xff;
			
			bytesSent = SoAd_SendIpMessage(sockNr, 8 + 7, txBuffer, 0);
			
			if (bytesSent != 8 + 7)
			{
				/*
				 * Failed to send the message. Report error to Det.
				 */
				//FK_TRACE_ERROR("SOAD_DOIP_ENTITY_STATUS_REQ_ID, SOAD_E_UNEXPECTED_EXECUTION\r\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_ENTITY_STATUS_REQ_ID, SOAD_E_UNEXPECTED_EXECUTION\r\n");
			}
			SoAd_BufferFree(txBuffer);
		}
		else
		{
			//FK_TRACE_ERROR("SOAD_DOIP_ENTITY_STATUS_REQ_ID, SOAD_E_NOBUFS\r\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_ENTITY_STATUS_REQ_ID, SOAD_E_NOBUFS\r\n");
			DoIP_createAndSendNack(sockNr, DOIP_E_OUT_OF_MEMORY);
		}
	}
	else
	{
		// Invalid payload length!
		//FK_TRACE_ERROR("SoAd_SocketClose\r\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SoAd_SocketClose\r\n");
		DoIP_createAndSendNack(sockNr, DOIP_E_INVALID_PAYLOAD_LENGTH);
		SoAd_SocketClose(sockNr);
	}
}

/**********************************************************
 * 函数名称:	handlePowerModeCheckReq
 * 函数作用:	负载类型为0x4003的电源模式请求处理
 * 函数参数:	[in] sockNr:			索引
 * 函数参数:	[in] payloadLength:		负载长度
 * 函数参数:	[in] rxBuffer:			负载数据
 * 函数返回:	无
**********************************************************/
static void handlePowerModeCheckReq(uint16 sockNr, uint32 payloadLength, uint8 *rxBuffer)
{
	// @req SWS_DoIP_00091
	if (0 == payloadLength)
	{
		uint8* txBuffer = NULL;
		if (SoAd_BufferGet(8 + 1, &txBuffer))
		{
			uint16 bytesSent;
			SoAd_DoIp_PowerMode powerMode;

			if (E_NOT_OK == gSoAdConfig.DoIpConfig->DoipPowerModeCallback(&powerMode))
			{
				// @req SWS_DoIP_00093
				powerMode = 0;
			}

			//FK_TRACE_INFO("powerMode: %d\r\n", powerMode);
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"powerMode: %d\r\n", powerMode);
			txBuffer[0] = DOIP_PROTOCOL_VERSION;
			txBuffer[1] = ~DOIP_PROTOCOL_VERSION;
			txBuffer[2] = 0x40;							// 0x4004->Power mode check response
			txBuffer[3] = 0x04;
			txBuffer[4] = 0;							// Length = 0x00000001
			txBuffer[5] = 0;
			txBuffer[6] = 0;
			txBuffer[7] = 1;

			txBuffer[8] = (uint8)powerMode;

			// @req SWS_DoIP_00092
			bytesSent = SoAd_SendIpMessage(sockNr, 8 + 1, txBuffer, 0);
			if (bytesSent != 8 + 1)
			{
				//FK_TRACE_ERROR("SOAD_DOIP_ENTITY_STATUS_REQ_ID, SOAD_E_UNEXPECTED_EXECUTION\r\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_ENTITY_STATUS_REQ_ID, SOAD_E_UNEXPECTED_EXECUTION\r\n");
			}
			SoAd_BufferFree(txBuffer);
		}
		else
		{
			//FK_TRACE_ERROR("SOAD_DOIP_ENTITY_STATUS_REQ_ID, SOAD_E_NOBUFS\r\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_ENTITY_STATUS_REQ_ID, SOAD_E_NOBUFS\r\n");
			DoIP_createAndSendNack(sockNr, DOIP_E_OUT_OF_MEMORY);
		}
	}
	else
	{
		// Invalid payload length!
		//FK_TRACE_ERROR("SoAd_SocketClose \r\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SoAd_SocketClose \r\n");
		DoIP_createAndSendNack(sockNr, DOIP_E_INVALID_PAYLOAD_LENGTH);
		SoAd_SocketClose(sockNr);
	}
}

/**********************************************************
 * 函数名称:	handleAliveCheckReq
 * 函数作用:	负载类型为0x0007的在线检查请求处理
 * 函数参数:	[in] sockNr:			索引
 * 函数参数:	[in] payloadLength:		负载长度
 * 函数参数:	[in] rxBuffer:			负载数据
 * 函数返回:	无
**********************************************************/
static void handleAliveCheckReq(uint16 sockNr, uint16 payloadLength, uint8* rxBuffer)
{
	uint16  i;
	uint16  sa;
	uint16  responses = 0;
	boolean checkAliveflag = FALSE;				//add fly
	uint16  checkAliveSocket = 0xff;			//add fly
	uint8   checkAliveTxBuffer[10] = {0};		//add fly
	
	for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++)
	{
		// FK_TRACE_INFO("sockNr = %d, sgSoAdDoip_cb.pConnectionStatus[%d].sockNr = %d\n", sockNr, i, sgSoAdDoip_cb.pConnectionStatus[i].sockNr);
		if (sgSoAdDoip_cb.pConnectionStatus[i].sockNr == sockNr)
		{
			// FK_TRACE_INFO("sgSoAdDoip_cb.pConnectionStatus[i].sa = %X, sa = %X\n", sgSoAdDoip_cb.pConnectionStatus[i].sa, sa);
			if (sgSoAdDoip_cb.pConnectionStatus[i].sa > 0)
			{
				// Alive check response received in time
				sgSoAdDoip_cb.pConnectionStatus[i].generalInactivityTimer = 0;
				sgSoAdDoip_cb.pConnectionStatus[i].awaitingAliveCheckResponse = FALSE;
				checkAliveSocket = sgSoAdDoip_cb.pConnectionStatus[i].sockNr;
				checkAliveflag = TRUE;
				sa = sgSoAdDoip_cb.pConnectionStatus[i].sa;
			}
			else
			{
				// Alive check response received from the wrong SA!
				// What to do?
			}
		}
	}
	
	for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++)
	{
		if (checkAliveflag == TRUE)			//add fly
		{
			responses++;
		}
	}
	
	if (responses > 0)
	{  
		uint16 bytesSentl;
		
		memset(checkAliveTxBuffer, 0, sizeof(checkAliveTxBuffer));
		
		if (checkAliveSocket != 0xff)
		{
			fillAliveCheckResponseData(checkAliveTxBuffer, sa);
			bytesSentl = SoAd_SendIpMessage(checkAliveSocket, 8 + 2, checkAliveTxBuffer, 0);
			if (bytesSentl != (8 + 2))
			{
				// Failed to send response. Connection probably already broken..
				//FK_TRACE_ERROR("DOIP_HANDLE_ALIVECHECK_RESP, SOAD_E_UNEXPECTED_EXECUTION\r\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"DOIP_HANDLE_ALIVECHECK_RESP, SOAD_E_UNEXPECTED_EXECUTION\n");
			}
		}
		else
		{
			//FK_TRACE_INFO("checkAliveTxBuffer error\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"checkAliveTxBuffer error\n");
		}

		checkAliveSocket = 0xff;
		checkAliveflag = FALSE;
	}
}

/**********************************************************
 * 函数名称:	handleAliveCheckResp
 * 函数作用:	负载类型为0x0008的在线检查响应处理
 * 函数参数:	[in] sockNr:			索引
 * 函数参数:	[in] payloadLength:		负载长度
 * 函数参数:	[in] rxBuffer:			负载数据
 * 函数返回:	无
**********************************************************/
static void handleAliveCheckResp(uint16 sockNr, uint16 payloadLength, uint8* rxBuffer)
{
	uint16 i;
	uint16 sa;
	DoIP_PthreadMutexLock();
	if (payloadLength == 2)
	{
		sa = (rxBuffer[8 + 0] << 8) | rxBuffer[8 + 1];

		for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++)
		{
			#if 0
			FK_TRACE_INFO("sgSoAdDoip_cb.pConnectionStatus[i].sockNr: %d, sgSoAdDoip_cb.pConnectionStatus[i].sa: %x, sockNr: %d, sa: %x\r\n", \
					sgSoAdDoip_cb.pConnectionStatus[i].sockNr, sgSoAdDoip_cb.pConnectionStatus[i].sa, sockNr, sa);
			#endif
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"sgSoAdDoip_cb.pConnectionStatus[i].sockNr: %d, sgSoAdDoip_cb.pConnectionStatus[i].sa: %x, sockNr: %d, sa: %x\r\n", \
					sgSoAdDoip_cb.pConnectionStatus[i].sockNr, sgSoAdDoip_cb.pConnectionStatus[i].sa, sockNr, sa);
			if (sgSoAdDoip_cb.pConnectionStatus[i].sockNr == sockNr) 
			{
				if (sgSoAdDoip_cb.pConnectionStatus[i].sa == sa) 
				{
					// Alive check response received in time
					sgSoAdDoip_cb.pConnectionStatus[i].generalInactivityTimer = 0;
					sgSoAdDoip_cb.pConnectionStatus[i].awaitingAliveCheckResponse = FALSE;
				} 
				else
				{
					// Alive check response received from the wrong SA!
					// What to do?
					DoIP_PthreadMutexUnlock();
					handleTimeout(i);   /** fly add,20221123 **/
					return;
				}
			}
		}

		// Connections remaining to receive alive check responses for
		uint16 remainingConnections = 0;
		for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++) 
		{
			if (TRUE == sgSoAdDoip_cb.pConnectionStatus[i].awaitingAliveCheckResponse) 
			{
				remainingConnections++;
			}
		}
		
		//FK_TRACE_INFO("remainingConnections: %d, sockNr: %d\r\n", remainingConnections, sockNr);
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"remainingConnections: %d, sockNr: %d\r\n", remainingConnections, sockNr);
		if (0 == remainingConnections) 
		{
			/*
			 * No remaining connections to receive alive check response for.
			 * This must mean that the incoming routing activation request must
			 * be denied...
			 */

			/* 0x01: Routing activation denied because all concurrently supported
			 *       TCP_DATA sockets are registered and active. */
			
			uint16 bytesSent;
			uint16 sendLen;
			uint8 routingActivationResponseCode = ROUTING_ACTIVATION_NO_SOEKET_USE;

			if (sgSoAdDoip_cb.PendingRoutingActivationSa == sa)
			{
				routingActivationResponseCode = ROUTING_ACTIVATION_DUP_REGISTERED;
			}
			
			if ((sgSoAdDoip_cb.PendingRoutingActivationSocket != 0xff) && (NULL != sgSoAdDoip_cb.pPendingRoutingActivationTxBuffer))
			{
				sendLen = fillRoutingActivationResponseData(sgSoAdDoip_cb.pPendingRoutingActivationTxBuffer, sgSoAdDoip_cb.PendingRoutingActivationSa, routingActivationResponseCode);
				DoIP_PthreadMutexUnlock();
				bytesSent = SoAd_SendIpMessage(sgSoAdDoip_cb.PendingRoutingActivationSocket, sendLen, sgSoAdDoip_cb.pPendingRoutingActivationTxBuffer, 0);
				if (bytesSent != sendLen)
				{
					// Failed to send response. Connection probably already broken..
					//FK_TRACE_ERROR("DOIP_HANDLE_ALIVECHECK_RESP, SOAD_E_UNEXPECTED_EXECUTION\r\n");
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"DOIP_HANDLE_ALIVECHECK_RESP, SOAD_E_UNEXPECTED_EXECUTION\r\n");
				}
				SoAd_BufferFree(sgSoAdDoip_cb.pPendingRoutingActivationTxBuffer);
				DoIP_PthreadMutexLock();
				#if 0
				FK_TRACE_ERROR("SoAd_SocketClose sgSoAdDoip_cb.PendingRoutingActivationSocket: %d, routingActivationResponseCode: %d\r\n", \
						sgSoAdDoip_cb.PendingRoutingActivationSocket, routingActivationResponseCode);
				#endif
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SoAd_SocketClose sgSoAdDoip_cb.PendingRoutingActivationSocket: %d, routingActivationResponseCode: %d\r\n", \
						sgSoAdDoip_cb.PendingRoutingActivationSocket, routingActivationResponseCode);
				SoAd_SocketClose(sgSoAdDoip_cb.PendingRoutingActivationSocket);
				sgSoAdDoip_cb.pPendingRoutingActivationTxBuffer = NULL;
				sgSoAdDoip_cb.PendingRoutingActivationSocket = 0xff;
			}
		}
	}
	DoIP_PthreadMutexUnlock();
}

/**********************************************************
 * 函数名称:	handleTimeout
 * 函数作用:	超时处理
 * 函数参数:	[in] connectionIndex:	索引
 * 函数返回:	无
**********************************************************/
static void handleTimeout(uint16 connectionIndex)
{
	uint16 sendLen,release_i = 0,NoRelease_fg = 0;
	uint16 bytesSent;
	uint8 routingActivationResponseCode = ROUTING_ACTIVATION_SUCCESSFULLY;

	/*
	 * Close current connection (which has timed out anyway)
	 * and register a new connection with the pending routing activation
	 */
	#if 0
	FK_TRACE_ERROR("SoAd_SocketClose, connectionIndex: %d, sockNr: %d\r\n", \
			 connectionIndex, sgSoAdDoip_cb.pConnectionStatus[connectionIndex].sockNr);
	#endif
	SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SoAd_SocketClose, connectionIndex: %d, sockNr: %d\r\n", \
			 connectionIndex, sgSoAdDoip_cb.pConnectionStatus[connectionIndex].sockNr);
	DoIP_PthreadMutexLock();
	SoAd_SocketClose(sgSoAdDoip_cb.pConnectionStatus[connectionIndex].sockNr);
	
	/* 只有tcp通道均显示为释放，才最终释放以太网通道 */
	for(release_i=0;release_i < SOAD_SOCKET_COUNT;release_i++){
		if((gsoad_extcb.SocketAdminList[release_i].SocketProtocolIsTcp) && (gsoad_extcb.SocketAdminList[release_i].SocketState == SOCKET_TCP_READY)){
			NoRelease_fg = 1;
		}
	}
	if(NoRelease_fg == 0){
		//FK_TRACE_INFO("channel reset\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"channel reset\n");
		if(gsoad_extcb.SetDgsChnStatus != NULL){
			gsoad_extcb.SetDgsChnStatus(0x02);
		}
	}

	if ((sgSoAdDoip_cb.PendingRoutingActivationSocket != 0xff) && (NULL != sgSoAdDoip_cb.pPendingRoutingActivationTxBuffer))
	{
		registerSocket(connectionIndex, sgSoAdDoip_cb.PendingRoutingActivationSocket, sgSoAdDoip_cb.PendingRoutingActivationType, sgSoAdDoip_cb.PendingRoutingActivationSa);

		sendLen = fillRoutingActivationResponseData(sgSoAdDoip_cb.pPendingRoutingActivationTxBuffer, sgSoAdDoip_cb.PendingRoutingActivationSa, routingActivationResponseCode);
		DoIP_PthreadMutexUnlock();
		bytesSent = SoAd_SendIpMessage(sgSoAdDoip_cb.PendingRoutingActivationSocket, sendLen, sgSoAdDoip_cb.pPendingRoutingActivationTxBuffer, 0);
		if (bytesSent != sendLen)
		{
			// Failed to send routing actication request..
			//FK_TRACE_ERROR("DOIP_HANDLE_ALIVECHECK_TIMEOUT, SOAD_E_UNEXPECTED_EXECUTION\r\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"DOIP_HANDLE_ALIVECHECK_TIMEOUT, SOAD_E_UNEXPECTED_EXECUTION\n");
		}
		SoAd_BufferFree(sgSoAdDoip_cb.pPendingRoutingActivationTxBuffer);
		sgSoAdDoip_cb.pPendingRoutingActivationTxBuffer = NULL;
		sgSoAdDoip_cb.PendingRoutingActivationSocket = 0xff;
	} 
	else 
	{
		// Ordinary timeout set connection as free...
		sgSoAdDoip_cb.pConnectionStatus[connectionIndex].socketState = DOIP_ARC_CONNECTION_INVALID;
		sgSoAdDoip_cb.pConnectionStatus[connectionIndex].awaitingAliveCheckResponse = FALSE;
		DoIP_PthreadMutexUnlock();
	}
}

static void associateTargetWithConnectionIndex(uint16 targetIndex, uint16 connectionIndex)
{
	sgSoAdDoip_cb.pTargetConnectionMap[targetIndex] = connectionIndex;
}

/**********************************************************
 * 函数名称:	handleDiagnosticMessage
 * 函数作用:	负载类型为0x8001的诊断请求报文处理
 * 函数参数:	[in] sockNr:			索引
 * 函数参数:	[in] payloadLength:		负载长度
 * 函数参数:	[in] rxBuffer:			负载数据
 * 函数返回:	无
**********************************************************/
static void handleDiagnosticMessage(uint16 sockNr, uint32 payloadLength, uint8 *rxBuffer)
{
	uint16 i;
	uint16 sa;
	uint16 ta;
	uint16 targetIndex;
	PduInfoType pduInfo;
	uint16 connectionIndex = 0xffff;
	LookupSaTaResultType lookupResult;
	uint16 diagnosticMessageLength = payloadLength - 4;

	if (payloadLength >= 4)
	{
		sa = (rxBuffer[8] << 8) | rxBuffer[9];
		ta = (rxBuffer[10] << 8) | rxBuffer[11];

		// Find connection entry for this socket
		for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++)
		{
			if ((sa == sgSoAdDoip_cb.pConnectionStatus[i].sa) && (sockNr == sgSoAdDoip_cb.pConnectionStatus[i].sockNr))
			{
				connectionIndex = i;
				break;
			}
		}

		if (gSoadRecvDebugLevel <= 0)
		{
			//FK_TRACE_INFO("connectionIndex: %d, sa: 0x%02X, ta: 0x%02X\r\n", connectionIndex, sa, ta);
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"connectionIndex: %d, sa: 0x%02X, ta: 0x%02X\r\n", connectionIndex, sa, ta);
		}

		lookupResult = lookupSaTa(connectionIndex, sa, ta, &targetIndex);
		//FK_TRACE_INFO("DoIP lookup result: %d [0:OK, 1:UNKNOWN, 2:ERR]\n", lookupResult);
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"DoIP lookup result: %d [0:OK, 1:UNKNOWN, 2:ERR]\n", lookupResult);
		if (gSoadRecvDebugLevel <= 1)
		{
			#if 0
			FK_TRACE_INFO("connectionIndex: %d, sa: 0x%02X, ta: 0x%02X, lookupResult: %d, targetIndex: %d\r\n", \
					connectionIndex, sa, ta, lookupResult, targetIndex);
			#endif
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"connectionIndex: %d, sa: 0x%02X, ta: 0x%02X, lookupResult: %d, targetIndex: %d\r\n", \
					connectionIndex, sa, ta, lookupResult, targetIndex);
		}

		if (lookupResult == LOOKUP_SA_TA_OK)
		{
			associateTargetWithConnectionIndex(targetIndex, connectionIndex);

			#if 0
			INT8U TAtype;
			uint16 connectionId;

			connectionId = sgSoAdDoip_cb.pTargetConnectionMap[targetIndex];

			if ((gSoAdConfig.DoIpTargetAddresses[targetIndex].addressValue == ta) && (targetIndex == 0))
			{
				TAtype = UDS_TATYPE_PHY;
			}
			else if ((gSoAdConfig.DoIpTargetAddresses[targetIndex].addressValue == ta) && (targetIndex == 1))
			{
				TAtype = UDS_TATYPE_FUN;
				//ta = sgSoAdDoip_cb.pConnectionStatus[connectionId].sa; // Target of response is the source of the initiating party...  test sa
				ta = gSoAdConfig.DoIpTargetAddresses[0].addressValue;
			}
			else
			{
				FK_TRACE_ERROR("TAtype error: %d\r\n", TAtype);
			}
			#endif

			if (SoAd_BufferGet(SOAD_RX_BUFFER_SIZE, &pduInfo.SduDataPtr))
			{
				uint8 channel = 0;
				uint16 recvLen = 0;

				pduInfo.SduLength = diagnosticMessageLength;
				// associateTargetWithConnectionIndex(targetIndex, connectionIndex);
				
				if (gSoadRecvDebugLevel <= 0)
				{
					//FK_TRACE_INFO("diagnosticMessageLength: %d\n", diagnosticMessageLength);
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"diagnosticMessageLength: %d\n", diagnosticMessageLength);
				}

				recv(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, rxBuffer, 12, 0);			/* Header + SA + TA */
				recvLen = recv(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, pduInfo.SduDataPtr, diagnosticMessageLength, 0);

				if (gSoadRecvDebugLevel <= 3)
				{
					//FK_TRACE_INFO("recvLen: %d\n", recvLen);
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"recvLen: %d\n", recvLen);
				}
				
				if (gSoadRecvDebugLevel <= 1)
				{
					//FK_TRACE_INFO_HEX(rxBuffer, 12);
					//FK_TRACE_INFO_HEX(pduInfo.SduDataPtr, 64);
				}

				if (pduInfo.SduLength > SOAD_RX_DIANOSTIC_MESSAGE_SIZE)
				{
					createAndSendDiagnosticNack(sockNr, sa, ta, DOIP_E_DIAG_MESSAGE_TO_LARGE);
				}
				else
				{
					#if (REFRESH_GENERAL_INACTIVITY_TIMER > 0)
					/* 若诊断仪未持续发送“在线检查请求”，即使有收到诊断请求，也会超时退出，这边对超时定时器进行刷新 */
					sgSoAdDoip_cb.pConnectionStatus[connectionIndex].generalInactivityTimer = 0;
					sgSoAdDoip_cb.pConnectionStatus[connectionIndex].awaitingAliveCheckResponse = FALSE;
					#endif

					createAndSendDiagnosticAck(sockNr, sa, ta);			// Send diagnostic message positive ack
					//DoIP_Server_Diagnostic_msg(sockNr, sa, ta, channel, pduInfo.SduDataPtr, diagnosticMessageLength);
					if(sgSoAdDoip_cb.pDoIPServerDiagnosticMsg != NULL){
						sgSoAdDoip_cb.pDoIPServerDiagnosticMsg(sockNr, sa, ta, channel, pduInfo.SduDataPtr, diagnosticMessageLength);
					}
				}

				SoAd_BufferFree(pduInfo.SduDataPtr);
			}
			else
			{
				createAndSendDiagnosticNack(sockNr, sa, ta, DOIP_E_DIAG_OUT_OF_MEMORY);
				discardIpMessage(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, payloadLength + 8, rxBuffer);
				// DET_REPORTERROR(MODULE_ID_SOAD, 0, SOAD_DOIP_HANDLE_DIAG_MSG_ID, SOAD_E_SHALL_NOT_HAPPEN);
			}
		}
		else if (lookupResult == LOOKUP_SA_TA_TAUNKNOWN)		// TA not known
		{
			createAndSendDiagnosticNack(sockNr, sa, ta, DOIP_E_DIAG_UNKNOWN_TA);
			discardIpMessage(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, payloadLength + 8, rxBuffer);
		}
		else													// SA not registered on receiving socket
		{
			//FK_TRACE_ERROR("SoAd_SocketClose, Invalid sa, sa: 0x%02X\n", sa);
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SoAd_SocketClose, Invalid sa, sa: 0x%02X\n", sa);
			createAndSendDiagnosticNack(sockNr, sa, ta, DOIP_E_DIAG_INVALID_SA);
			SoAd_SocketClose(sockNr);
		}
	}
	else 
	{
		//FK_TRACE_ERROR("SoAd_SocketClose, Invalid payload length, len: %d\n", payloadLength);
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SoAd_SocketClose, Invalid payload length, len: %d\n", payloadLength);
		DoIP_createAndSendNack(sockNr, DOIP_E_INVALID_PAYLOAD_LENGTH);
		SoAd_SocketClose(sockNr);
	}
}

/**********************************************************
 * 函数名称:	DoIP_HandleTcpRx
 * 函数作用:	TCP-DoIP消息处理
 * 函数参数:	[in] sockNr:	索引
 * 函数返回:	无
**********************************************************/
void DoIP_HandleTcpRx(uint16 sockNr)
{
	int i;
	int nBytes;
	uint8* rxBuffer;
	uint16 payloadType;
	uint16 payloadLength;

	if (SoAd_BufferGet(SOAD_RX_BUFFER_SIZE, &rxBuffer))
	{
		nBytes = recv(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, rxBuffer, SOAD_RX_BUFFER_SIZE, MSG_PEEK);
		if (nBytes == 0)			/* 对端已关闭 */
		{
			//FK_TRACE_INFO("diagnostic device disconnected from TCP connection\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"diagnostic device disconnected from TCP connection\n");
			SoAd_BufferFree(rxBuffer);

			DoIP_PthreadMutexLock();
			SoAd_SocketClose(sockNr);
			
			for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++)
			{
				if ((sgSoAdDoip_cb.pConnectionStatus[i].sockNr == sockNr) && (DOIP_ARC_CONNECTION_REGISTERED == sgSoAdDoip_cb.pConnectionStatus[i].socketState))
				{
					sgSoAdDoip_cb.pConnectionStatus[i].socketState = DOIP_ARC_CONNECTION_INVALID;		/** add by fly **/
					sgSoAdDoip_cb.pConnectionStatus[i].awaitingAliveCheckResponse = FALSE;				/** add by fly **/
				}
			}
			DoIP_PthreadMutexUnlock();
			return;
		}
		
		SoAd_SocketStatusCheck(sockNr, gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle);

		if (gSoadRecvDebugLevel <= 1)
		{
			#if 0
			FK_TRACE_INFO("TCP recv data, nBytes: %d, sockNr: %d, ConnectionHandle: %d\n", \
					nBytes, sockNr, gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle);
			FK_TRACE_INFO_HEX(&rxBuffer[0], 8);
			#endif
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"TCP recv data, nBytes: %d, sockNr: %d, ConnectionHandle: %d\n", \
					nBytes, sockNr, gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle);
		}
		
		if (nBytes >= 8)
		{
			/* TODO REMOVE WHEN MOVED TO CANOE8.1 */
			if ((rxBuffer[0] == DOIP_PROTOCOL_VERSION) && ((uint8)(~rxBuffer[1]) == DOIP_PROTOCOL_VERSION))
			{
				payloadType   = (rxBuffer[2] << 8) | rxBuffer[3];
				payloadLength = (rxBuffer[4] << 24) | (rxBuffer[5] << 16) | (rxBuffer[6] << 8) | rxBuffer[7];

				if ((payloadLength + 8) <= SOAD_RX_BUFFER_SIZE)
				{
					if ((payloadLength + 8) <= nBytes)
					{
						switch (payloadType)
						{
							case PAYLOAD_TYPE_ROUTING_ACTIVATION:		// Routing Activation request
							{
								//FK_TRACE_INFO("tcp handleRoutingActivationReq\r\n");
								SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"tcp handleRoutingActivationReq\r\n");
								nBytes = recv(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, rxBuffer, payloadLength + 8, 0);
								handleRoutingActivationReq(sockNr, payloadLength, rxBuffer);
								break;
							}
							case PAYLOAD_TYPE_ALIVE_CHECK_REQUEST:		// Alive check request from other doip entity, if the doip entity is gateway, it should deel with it
							{
								if (gSoadRecvDebugLevel <= 1)
								{
									//FK_TRACE_INFO("tcp handleAliveCheckReq, sockNr: %d\n", sockNr);
									SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"tcp handleAliveCheckReq, sockNr: %d\n", sockNr);
								}
								
								nBytes = recv(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, rxBuffer, payloadLength + 8, 0);
								handleAliveCheckReq(sockNr, payloadLength, rxBuffer);
								break;
							}
							case PAYLOAD_TYPE_ALIVE_CHECK_RESPONSE:		// Alive check response
							{
								if (gSoadRecvDebugLevel <= 1)
								{
									//FK_TRACE_INFO("tcp handleAliveCheckResp, sockNr: %d\n", sockNr);
									SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"tcp handleAliveCheckResp, sockNr: %d\n", sockNr);
								}
								
								nBytes = recv(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, rxBuffer, payloadLength + 8, 0);
								handleAliveCheckResp(sockNr, payloadLength, rxBuffer);
								break;
							}
							case PAYLOAD_TYPE_DIAGNOSTIC_MESSAGE:		// Diagnostic message
							{
								if (gSoadRecvDebugLevel <= 1)
								{
									//FK_TRACE_INFO("tcp 0x8001 handleDiagnosticMessage\r\n");
									SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"tcp 0x8001 handleDiagnosticMessage\r\n");
								}
								
								handleDiagnosticMessage(sockNr, payloadLength, rxBuffer);
								break;
							}
							default:
							{
								DoIP_createAndSendNack(sockNr, DOIP_E_UNKNOWN_PAYLOAD_TYPE);
								discardIpMessage(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, payloadLength + 8, rxBuffer);
								break;
							}
						}
					}
					#if 0		/* 若应用层未读取到所有的TCP流数据, 会导致这边断开与诊断仪的连接 */
					else
					{
						FK_TRACE_ERROR("invalid payload length, payloadLen: %d, nBytes: %d\n", payloadLength, nBytes);
						DoIP_createAndSendNack(sockNr, DOIP_E_INVALID_PAYLOAD_LENGTH);
						SoAd_SocketClose(sockNr);
					}
					#endif
				}
				else
				{
					//FK_TRACE_ERROR("DoIP message to large, len: %d\n", payloadLength);
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"DoIP message to large, len: %d\n", payloadLength);
					DoIP_createAndSendNack(sockNr, DOIP_E_MESSAGE_TO_LARGE);
					discardIpMessage(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, payloadLength + 8, rxBuffer);
				}
			}
			else
			{
				//FK_TRACE_ERROR("TCP SoAd_SocketClose, DoIP version: [0x%X][0x%X]\n", rxBuffer[0], (uint8)(~rxBuffer[1]));
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"TCP SoAd_SocketClose, DoIP version: [0x%X][0x%X]\n", rxBuffer[0], (uint8)(~rxBuffer[1]));
				DoIP_createAndSendNack(sockNr, DOIP_E_INCORRECT_PATTERN_FORMAT);
				SoAd_SocketClose(sockNr);
			}
		}

		SoAd_BufferFree(rxBuffer);
	}
	else
	{
		// No rx buffer available. Report this in Det. Message should be handled in the next (scanSockets) loop.
		//FK_TRACE_ERROR("SOAD_DOIP_HANDLE_TCP_RX_ID, SOAD_E_NOBUFS\r\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_HANDLE_TCP_RX_ID, SOAD_E_NOBUFS\n");
	}
}

/**********************************************************
 * 函数名称:	isUdpPortValid
 * 函数作用:	判断uds端口是否有效
 * 函数参数:	[in] port:		udp端口号
 * 函数返回:	端口有效返回true, 端口无效返回false
 * 注意事项:	DoIP-135
**********************************************************/
static boolean isUdpPortValid(uint16 port)
{
	if ((port >= DOIP_UDP_PORT_MIN) && (port <= DOIP_UDP_PORT_MAX))
	{
		return true;
	}

	return false;
}

/**********************************************************
 * 函数名称:	checkDoipVersion
 * 函数作用:	检查DoIP版本号
 * 函数参数:	[in] ver:				版本号
 * 函数参数:	[in] verInvert:			版本号取反
 * 函数参数:	[in] payloadType:		DoIP-Payload类型
 * 函数返回:	检查通过返回true, 检查失败返回false
**********************************************************/
static boolean checkDoipVersion(uint8 ver, uint8 verInvert, uint16 payloadType)
{
	boolean isValid = false;

	if ((ver == DOIP_PROTOCOL_VERSION) && ((uint8)(~verInvert) == DOIP_PROTOCOL_VERSION))
	{
		isValid = true;
	}
	else if ((ver == DOIP_PROTOCOL_VERSION_DEFAULT) && ((uint8)(~verInvert) == DOIP_PROTOCOL_VERSION_DEFAULT))
	{
		/* 诊断仪在发送车辆识别请求时，使用协议版本的默认值0xFF，DoIP实体对版本号予以忽略 */
		if (payloadType == PAYLOAD_TYPE_IDREQUEST \
				|| payloadType == PAYLOAD_TYPE_IDREQUEST_BY_EID \
				|| payloadType == PAYLOAD_TYPE_IDREQUEST_BY_VIN)
		{
			isValid = true;
		}
	}

	return isValid;
}

/**********************************************************
 * 函数名称:	DoIP_HandleUdpRx
 * 函数作用:	UDP-DoIP消息处理
 * 函数参数:	[in] sockNr:	索引
 * 函数返回:	无
**********************************************************/
void DoIP_HandleUdpRx(uint16 sockNr)
{
	int nBytes;
	uint8* rxBuffer;
	uint16 payloadType;
	uint16 payloadLength;
	struct sockaddr_in fromAddr;
	socklen_t fromAddrLen = sizeof(fromAddr);

	if (SoAd_BufferGet(SOAD_RX_BUFFER_SIZE, &rxBuffer))
	{
		nBytes = recvfrom(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, rxBuffer, \
				SOAD_RX_BUFFER_SIZE, MSG_PEEK, (struct sockaddr*)&fromAddr, &fromAddrLen);
		gsoad_extcb.SocketAdminList[sockNr].RemotePort		= fromAddr.sin_port;
		gsoad_extcb.SocketAdminList[sockNr].RemoteIpAddress	= fromAddr.sin_addr.s_addr;

		SoAd_SocketStatusCheck(sockNr, gsoad_extcb.SocketAdminList[sockNr].SocketHandle);

		if (gSoadRecvDebugLevel <= 3)
		{
			//FK_TRACE_INFO("fromAddr.sin_port: %d\r\n", ntohs(fromAddr.sin_port));
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"fromAddr.sin_port: %d\r\n", ntohs(fromAddr.sin_port));
		}
		
		if (!isUdpPortValid(ntohs(fromAddr.sin_port)))
		{
			#if 0
			FK_TRACE_WARN("src port %d for the udp msg is not within the range [%d, %d]\n", 
					ntohs(fromAddr.sin_port), DOIP_UDP_PORT_MIN, DOIP_UDP_PORT_MAX);
			#endif
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"src port %d for the udp msg is not within the range [%d, %d]\n", 
					ntohs(fromAddr.sin_port), DOIP_UDP_PORT_MIN, DOIP_UDP_PORT_MAX);
			discardIpMessage(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, nBytes, rxBuffer);
			SoAd_BufferFree(rxBuffer);
			return;
		}
		
		if (gSoadRecvDebugLevel <= 3)
		{
			#if 0
			FK_TRACE_INFO("udp nBytes: %d, sockNr: %d, SocketHandle: %d, rxbuffer: %x,%x,%x,%x\r\n", \
					nBytes, sockNr, gsoad_extcb.SocketAdminList[sockNr].SocketHandle, \
					rxBuffer[0], rxBuffer[1], rxBuffer[2], rxBuffer[3]);
			#endif
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"udp nBytes: %d, sockNr: %d, SocketHandle: %d, rxbuffer: %x,%x,%x,%x\r\n", \
					nBytes, sockNr, gsoad_extcb.SocketAdminList[sockNr].SocketHandle, \
					rxBuffer[0], rxBuffer[1], rxBuffer[2], rxBuffer[3]);
		}
		
		if (nBytes >= 8)
		{
			if (checkDoipVersion(rxBuffer[0], rxBuffer[1], rxBuffer[2] << 8 | rxBuffer[3]))
			{
				payloadType   = (rxBuffer[2] << 8) | rxBuffer[3];
				payloadLength = (rxBuffer[4] << 24) | (rxBuffer[5] << 16) | (rxBuffer[6] << 8) | rxBuffer[7];
				
				if ((payloadLength + 8) <= SOAD_RX_BUFFER_SIZE)
				{
					if ((payloadLength + 8) <= nBytes)
					{
						nBytes = recvfrom(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, rxBuffer, \
								payloadLength + 8, 0, (struct sockaddr*)&fromAddr, &fromAddrLen);
						gsoad_extcb.SocketAdminList[sockNr].RemotePort		= fromAddr.sin_port;
						gsoad_extcb.SocketAdminList[sockNr].RemoteIpAddress	= fromAddr.sin_addr.s_addr;
						
						if (gSoadRecvDebugLevel <= 3)
						{
							#if 0	
							FK_TRACE_INFO("payloadType: 0x%04X, %d, 0x%08X\r\n",\
									payloadType, gsoad_extcb.SocketAdminList[sockNr].RemotePort, gsoad_extcb.SocketAdminList[sockNr].RemoteIpAddress);
							#endif
							SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"payloadType: 0x%04X, %d, 0x%08X\r\n",\
									payloadType, gsoad_extcb.SocketAdminList[sockNr].RemotePort, gsoad_extcb.SocketAdminList[sockNr].RemoteIpAddress);
						}
						
						switch (payloadType)
						{
							case PAYLOAD_TYPE_NACK:							/* DoIP首部否定响应 */
							{
								break;
							}
							case PAYLOAD_TYPE_IDREQUEST:					/* 车辆识别请求 */
							{
								if (gSoadRecvDebugLevel <= 3)
								{
									//FK_TRACE_INFO("udp handleVehicleIdentificationReq\r\n");
									SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"udp handleVehicleIdentificationReq\r\n");
								}
								handleVehicleIdentificationReq(sockNr, payloadLength, rxBuffer, SOAD_ARC_DOIP_IDENTIFICATIONREQUEST_ALL);
								break;
							}
							case PAYLOAD_TYPE_IDREQUEST_BY_EID:				/* 带EID的车辆识别请求 */
							{
								if (gSoadRecvDebugLevel <= 3)
								{
									//FK_TRACE_INFO("udp Vehicle Identification Request with EID\r\n");
									SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"udp Vehicle Identification Request with EID\r\n");
								}
								handleVehicleIdentificationReq(sockNr, payloadLength, rxBuffer, SOAD_ARC_DOIP_IDENTIFICATIONREQUEST_BY_EID);
								break;
							}
							case PAYLOAD_TYPE_IDREQUEST_BY_VIN:				/* 带VIN的车辆识别请求 */
							{
								if (gSoadRecvDebugLevel <= 3)
								{
									//FK_TRACE_INFO("udp Vehicle Identification Request with VIN\r\n");
									SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"udp Vehicle Identification Request with VIN\r\n");
								}
								handleVehicleIdentificationReq(sockNr, payloadLength, rxBuffer, SOAD_ARC_DOIP_IDENTIFICATIONREQUEST_BY_VIN);
								break;
							}
							case PAYLOAD_TYPE_ANNOUNCEMENT:					/* 车辆声明/车辆识别响应 */
							{
								if (gSoadRecvDebugLevel <= 3)
								{
									//FK_TRACE_INFO("DoIP_HandleUdpRx receive announcement or identify, payloadLength: %d\n", payloadLength);
									SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"DoIP_HandleUdpRx receive announcement or identify, payloadLength: %d\n", payloadLength);
								}
								break;
							}
							case PAYLOAD_TYPE_ROUTING_ACTIVATION:			/* 路由激活请求 */
							{
								break;
							}
							case PAYLOAD_TYPE_STATUS_REQUEST:				/* DoIP实体状态请求 */
							{
								if (gSoadRecvDebugLevel <= 3)
								{
									//FK_TRACE_INFO("udp handleEntityStatusReq\r\n");
									SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"udp handleEntityStatusReq\r\n");
								}
								handleEntityStatusReq(sockNr, payloadLength, rxBuffer);
								break;
							}
							case PAYLOAD_TYPE_POWERMOE_REQUEST:				/* 诊断电源模式请求 */
							{
								if (gSoadRecvDebugLevel <= 3)
								{
									//FK_TRACE_INFO("udp handlePowerModeCheckReq\r\n");
									SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"udp handlePowerModeCheckReq\r\n");
								}
								handlePowerModeCheckReq(sockNr, payloadLength, rxBuffer);
								break;
							}
							default:
							{
								DoIP_createAndSendNack(sockNr, DOIP_E_UNKNOWN_PAYLOAD_TYPE);
								discardIpMessage(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, payloadLength + 8, rxBuffer);
								break;
							}
						}
					}
					else
					{
						//FK_TRACE_ERROR("udp SoAd_SocketClose, invalid payload length, len: %d\n", payloadLength);
						SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"udp SoAd_SocketClose, invalid payload length, len: %d\n", payloadLength);
						DoIP_createAndSendNack(sockNr, DOIP_E_INVALID_PAYLOAD_LENGTH);
						SoAd_SocketClose(sockNr);
					}
				}
				else 
				{
					//FK_TRACE_ERROR("DoIP message to large, len: %d\n", payloadLength);
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"DoIP message to large, len: %d\n", payloadLength);
					DoIP_createAndSendNack(sockNr, DOIP_E_MESSAGE_TO_LARGE);
					discardIpMessage(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, payloadLength + 8, rxBuffer);
				}
			}
			else 
			{
				//FK_TRACE_ERROR("SoAd_SocketClose, sockNr: %d, version: [0x%X, 0x%X] \r\n", sockNr, rxBuffer[0], rxBuffer[1]);
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SoAd_SocketClose, sockNr: %d, version: [0x%X, 0x%X] \r\n", sockNr, rxBuffer[0], rxBuffer[1]);
				DoIP_createAndSendNack(sockNr, DOIP_E_INCORRECT_PATTERN_FORMAT);
				SoAd_SocketClose(sockNr);
			}
		}

		SoAd_BufferFree(rxBuffer);
	}
	else 
	{
		// No rx buffer available. Report this in Det. Message should be handled in the next (scanSockets) loop.
		//FK_TRACE_ERROR("SOAD_DOIP_HANDLE_UDP_RX_ID, SOAD_E_NOBUFS\r\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_HANDLE_UDP_RX_ID, SOAD_E_NOBUFS\r\n");
		DoIP_createAndSendNack(sockNr, DOIP_E_OUT_OF_MEMORY);
	}
}

/**********************************************************
 * 函数名称:	DoIP_PthreadMutexLock
 * 函数作用:	加互斥锁
 * 函数参数:	无
 * 函数返回:	无
**********************************************************/
void DoIP_PthreadMutexLock(void)
{
	assert(pthread_mutex_lock(&sgSoAdDoip_cb.MutexSend) == 0);
}

/**********************************************************
 * 函数名称:	DoIP_PthreadMutexUnlock
 * 函数作用:	解互斥锁
 * 函数参数:	无
 * 函数返回:	无
**********************************************************/
void DoIP_PthreadMutexUnlock(void)
{
	assert(pthread_mutex_unlock(&sgSoAdDoip_cb.MutexSend) == 0);
}

/**********************************************************
 * 函数名称:	DoIP_HandleTpTransmit
 * 函数作用:	数据发送
 * 函数参数:	[in] socketNr:				索引
 * 函数参数:	[in] SoAdSrcPduInfoPtr:		数据信息
 * 函数返回:	见 Std_ReturnType
**********************************************************/
Std_ReturnType DoIP_HandleTpTransmit(uint16 socketNr, PduInfoType* SoAdSrcPduInfoPtr)
{
	uint16 ta;
	uint16 sa;
	uint16 bytesSent;
	PduInfoType txPduInfo;
	PduInfoType txPayloadPduInfo;
	Std_ReturnType returnCode = E_OK;
	SocketAdminType SocketAdmincurrent;

#if 0
	uint16 i;
	uint16 targetIndex = 0xffff;
	for (i = 0; i < DOIP_TARGET_COUNT; i++)
	{
		if (gSoAdConfig.DoIpTargetAddresses[i].rxPdu == SoAdSrcPduId)
		{
			// Match!
			targetIndex = i;
			break;
		}
	}

	if (targetIndex == 0xffff)
	{
		// Did not find corresponding target. Most likely due to faulty configuration.
		return E_NOT_OK;
	}
#endif

	sa = SoAdSrcPduInfoPtr->SduDataPtr[0] << 8 | SoAdSrcPduInfoPtr->SduDataPtr[1];
	ta = SoAdSrcPduInfoPtr->SduDataPtr[2] << 8 | SoAdSrcPduInfoPtr->SduDataPtr[3];
	SoAdSrcPduInfoPtr->SduDataPtr += 4;
	SoAdSrcPduInfoPtr->SduLength  -= 4;
	
	DoIP_PthreadMutexLock();
	memcpy(&SocketAdmincurrent, &gsoad_extcb.SocketAdminList[socketNr], sizeof(SocketAdminType));
	DoIP_PthreadMutexUnlock();
	
	if ((SocketAdmincurrent.SocketState == SOCKET_TCP_LISTENING) \
			|| (SocketAdmincurrent.SocketState == SOCKET_TCP_READY) \
			|| (SocketAdmincurrent.SocketState == SOCKET_UDP_READY))
	{
		txPduInfo.SduLength = SoAdSrcPduInfoPtr->SduLength + 12;		// Make room for extra doip header
		if (SoAd_BufferGet(txPduInfo.SduLength, &txPduInfo.SduDataPtr))
		{
#if 0
			uint16 connectionId = sgSoAdDoip_cb.pTargetConnectionMap[targetIndex];
			uint16 ta = sgSoAdDoip_cb.pConnectionStatus[connectionId].sa; 				// Target of response is the source of the initiating party...  test sa
			uint16 sa = gSoAdConfig.DoIpTargetAddresses[targetIndex].addressValue;
#endif

			txPduInfo.SduDataPtr[0] = DOIP_PROTOCOL_VERSION;
			txPduInfo.SduDataPtr[1] = ~DOIP_PROTOCOL_VERSION;
			txPduInfo.SduDataPtr[2] = 0x80;								// 0x8001->Diagnostic message
			txPduInfo.SduDataPtr[3] = 0x01;
			txPduInfo.SduDataPtr[4] = (SoAdSrcPduInfoPtr->SduLength + 4) >> 24;
			txPduInfo.SduDataPtr[5] = (SoAdSrcPduInfoPtr->SduLength + 4) >> 16;
			txPduInfo.SduDataPtr[6] = (SoAdSrcPduInfoPtr->SduLength + 4) >> 8;
			txPduInfo.SduDataPtr[7] = (SoAdSrcPduInfoPtr->SduLength + 4) >> 0;

			txPduInfo.SduDataPtr[8] = sa >> 8;
			txPduInfo.SduDataPtr[9] = sa >> 0;

			txPduInfo.SduDataPtr[10] = ta >> 8;
			txPduInfo.SduDataPtr[11] = ta >> 0;

			// Copy the Pdu to the tx buffer
			txPayloadPduInfo.SduDataPtr = txPduInfo.SduDataPtr + 12;	// The actual SDU payload is after header
			memcpy(txPayloadPduInfo.SduDataPtr, SoAdSrcPduInfoPtr->SduDataPtr, SoAdSrcPduInfoPtr->SduLength);
			txPayloadPduInfo.SduLength = SoAdSrcPduInfoPtr->SduLength;

			// Then send the diagnostic message and confirm transmission to PduR
			bytesSent = SoAd_SendIpMessage(socketNr, txPduInfo.SduLength, txPduInfo.SduDataPtr, 0);

			if (bytesSent == txPduInfo.SduLength) 
			{
				SoAd_BufferFree(txPduInfo.SduDataPtr);

				// SendConfirmation(0, NTFRSLT_OK);
				// FK_TRACE_INFO("SoAd_SendIpMessage, NTFRSLT_OK\r\n");
			}
			else
			{
				// All bytes were not sent - discarding this and setting a Det error instead.
				SoAd_BufferFree(txPduInfo.SduDataPtr);
				//FK_TRACE_ERROR("SOAD_DOIP_HANDLE_TP_TRANSMIT_ID, NTFRSLT_E_NOT_OK\r\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_HANDLE_TP_TRANSMIT_ID, NTFRSLT_E_NOT_OK\r\n");

				// Notify PduR abotu failed transmission.
				// SendConfirmation(0, NTFRSLT_E_NOT_OK);
			}
		}
		else
		{
			// No buffer to send with. Report failure back to PduR.
			//FK_TRACE_ERROR("SOAD_DOIP_HANDLE_TP_TRANSMIT_ID, SOAD_E_NOBUFS\r\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_DOIP_HANDLE_TP_TRANSMIT_ID, SOAD_E_NOBUFS\r\n");
			// SendConfirmation(0, NTFRSLT_E_NO_BUFFER);
		}
	}
	else 
	{
		/* Socket not ready */
		returnCode = E_NOT_OK;
	}

	return returnCode;
}

/**********************************************************
 * 函数名称:	tcpInitialInactivityTimerHandle
 * 函数作用:	DoIP路由激活超时检查
 * 函数参数:	无
 * 函数返回:	无
**********************************************************/
static void tcpInitialInactivityTimerHandle(void)
{
	uint16 i, j;
	bool isRouting;
	DoIP_PthreadMutexLock();
	for (i = 0; i < SOAD_SOCKET_COUNT; i++)
	{
		if (gsoad_extcb.SocketAdminList[i].SocketProtocolIsTcp \
				&& (gsoad_extcb.SocketAdminList[i].SocketState == SOCKET_TCP_READY)
				&& (gsoad_extcb.SocketAdminList[i].ConnectionHandle != -1))
		{
			isRouting = false;
		
			for (j = 0; j < sgSoAdDoip_cb.DoipMaxTesterConnection; j++)
			{
				if (sgSoAdDoip_cb.pConnectionStatus[j].sockNr == i)
				{
					isRouting = true;
					break;
				}
			}

			if (!isRouting)
			{
				if (gsoad_extcb.SocketAdminList[i].TcpinitialInactivityTimer < DOIP_INITIALINACTIVETIMEMS)
				{
					gsoad_extcb.SocketAdminList[i].TcpinitialInactivityTimer += DOIP_MAINFUNCTION_PERIOD_TIME;
				}
				else
				{
					#if 0
					FK_TRACE_INFO("DoIpTCPInitialInactiveTimeMs timeout, sockNr: %d, TcpinitialInactivityTimer: %d\n", \
							i, gsoad_extcb.SocketAdminList[i].TcpinitialInactivityTimer);
					#endif
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"DoIpTCPInitialInactiveTimeMs timeout, sockNr: %d, TcpinitialInactivityTimer: %d\n", \
							i, gsoad_extcb.SocketAdminList[i].TcpinitialInactivityTimer);
					SoAd_SocketClose(i);
				}
			}
		}
	}
	DoIP_PthreadMutexUnlock();
}

/**********************************************************
 * 函数名称:	announcementTimerHandle
 * 函数作用:	车辆声明发送处理
 * 函数参数:	无
 * 函数返回:	无
**********************************************************/
static void announcementTimerHandle(void)
{
	DoIP_PthreadMutexLock();
	if (DOIP_LINK_UP == sgSoAdDoip_cb.LinkStatus)			// use DoIP_LocalIpAddrAssignmentChg function init sgSoAdDoip_cb.DoipArcAnnounceWait
	{
		/* FK_TRACE_INFO("sgSoAdDoip_cb.DoipArcAnnouncementTimer: %d, sgSoAdDoip_cb.DoipArcAnnounceWait: %d\n", \
				sgSoAdDoip_cb.DoipArcAnnouncementTimer, sgSoAdDoip_cb.DoipArcAnnounceWait); */

		sgSoAdDoip_cb.DoipArcAnnouncementTimer += DOIP_MAINFUNCTION_PERIOD_TIME;
		
		if ((sgSoAdDoip_cb.DoipArcAnnounceWait <= sgSoAdDoip_cb.DoipArcAnnouncementTimer) \
				&& ((sgSoAdDoip_cb.DoipArcAnnounceWait + SOAD_DOIP_ANNOUNCE_NUM * SOAD_DOIP_ANNOUNCE_INTERVAL) > sgSoAdDoip_cb.DoipArcAnnouncementTimer))
		{
			/* Within announcement period */
			uint32 timer = sgSoAdDoip_cb.DoipArcAnnouncementTimer - sgSoAdDoip_cb.DoipArcAnnounceWait;

			if (sgSoAdDoip_cb.NumAnnouncements * SOAD_DOIP_ANNOUNCE_INTERVAL <= timer)
			{
				/* Time to send another announcement */
				DoIP_PthreadMutexUnlock();
				DoIP_SendVehicleAnnouncement(SOAD_DOIP_ANNOUNCE_SOCKET);
				DoIP_PthreadMutexLock();
				sgSoAdDoip_cb.NumAnnouncements++;
			}
		}
		else
		{
			sgSoAdDoip_cb.NumAnnouncements = 0;
		}
	}
	else 
	{
		sgSoAdDoip_cb.NumAnnouncements = 0;
	}
	DoIP_PthreadMutexUnlock();
}

/**********************************************************
 * 函数名称:	connectionTimeoutHandle
 * 函数作用:	DoIP连接超时检查
 * 函数参数:	无
 * 函数返回:	无
**********************************************************/
static void connectionTimeoutHandle(void)
{
	uint16 i;
	DoIP_PthreadMutexLock();
	/*
	 * Handle DoIP connection timeouts..
	 */
	for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++)
	{
		if (DOIP_ARC_CONNECTION_REGISTERED == sgSoAdDoip_cb.pConnectionStatus[i].socketState)
		{
			/*
			 * Handle Alive check timeouts
			 */
			if (TRUE == sgSoAdDoip_cb.pConnectionStatus[i].awaitingAliveCheckResponse)
			{
				if (sgSoAdDoip_cb.pConnectionStatus[i].aliveCheckTimer >= DOIP_ALIVECHECK_RESPONSE_TIMEOUT)
				{
					#if 0
					FK_TRACE_INFO("aliveCheckTimer timeout, i: %d, aliveCheckTimer: %d\n", \
							i, sgSoAdDoip_cb.pConnectionStatus[i].aliveCheckTimer);
					#endif
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"aliveCheckTimer timeout, i: %d, aliveCheckTimer: %d\n", \
							i, sgSoAdDoip_cb.pConnectionStatus[i].aliveCheckTimer);
					DoIP_PthreadMutexUnlock();
					handleTimeout(i);
					DoIP_PthreadMutexLock();
				} 
				else
				{
					sgSoAdDoip_cb.pConnectionStatus[i].aliveCheckTimer += DOIP_MAINFUNCTION_PERIOD_TIME;
				}
			}

			/* FK_TRACE_INFO("generalInactivityTimer timeout, i: %d, generalInactivityTimer: %d, %d\n", \
					i, sgSoAdDoip_cb.pConnectionStatus[i].generalInactivityTimer, DOIP_GENERAL_INACTIVITY_TIMEOUT); */

			/*
			 * Handle general inactivity timeouts
			 */
			if ((sgSoAdDoip_cb.pConnectionStatus[i].generalInactivityTimer < DOIP_GENERAL_INACTIVITY_TIMEOUT) \
					|| (sgSoAdDoip_cb.pConnectionStatus[i].generalInactivityTimer <= DOIP_ALIVECHECK_RESPONSE_TIMEOUT))
			{
				sgSoAdDoip_cb.pConnectionStatus[i].generalInactivityTimer += DOIP_MAINFUNCTION_PERIOD_TIME;
			}
			else
			{
				#if 0
				FK_TRACE_INFO("generalInactivityTimer timeout, i: %d, generalInactivityTimer: %d\n", \
						i, sgSoAdDoip_cb.pConnectionStatus[i].generalInactivityTimer);
				#endif
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"generalInactivityTimer timeout, i: %d, generalInactivityTimer: %d\n", \
						i, sgSoAdDoip_cb.pConnectionStatus[i].generalInactivityTimer);
				DoIP_PthreadMutexUnlock();
				handleTimeout(i);
				DoIP_PthreadMutexLock();
			}

			/*
			 * TODO: Handle initial inactivity timeouts
			 */
			#if 0
			if ((sgSoAdDoip_cb.pConnectionStatus[i].initialInactivityTimer <= DOIP_GENERAL_INACTIVITY_TIMEOUT) \
					|| (sgSoAdDoip_cb.pConnectionStatus[i].initialInactivityTimer <= DOIP_ALIVECHECK_RESPONSE_TIMEOUT))
			{
				sgSoAdDoip_cb.pConnectionStatus[i].initialInactivityTimer += DOIP_MAINFUNCTION_PERIOD_TIME;
			}
			#endif
		}
	}
	DoIP_PthreadMutexUnlock();
}

/**********************************************************
 * 函数名称:	DoIP_MainFunction
 * 函数作用:	主函数
 * 函数参数:	无
 * 函数返回:	无
**********************************************************/
void DoIP_MainFunction(void)
{
	connectionTimeoutHandle();
	announcementTimerHandle();
	tcpInitialInactivityTimerHandle();
}

/**********************************************************
 * 函数名称:	DoIP_TcpCloseAtDiagSessionSwitch
 * 函数作用:	UDS会话切换时关闭DoIP连接
 * 函数参数:	[in] connectionIndex:		索引值
 * 函数返回:	无
**********************************************************/
void DoIP_TcpCloseAtDiagSessionSwitch(uint16 connectionIndex)
{
	DoIP_PthreadMutexLock();
	SoAd_SocketClose(sgSoAdDoip_cb.pConnectionStatus[connectionIndex].sockNr);
	sgSoAdDoip_cb.pConnectionStatus[connectionIndex].socketState = DOIP_ARC_CONNECTION_INVALID;
	sgSoAdDoip_cb.pConnectionStatus[connectionIndex].awaitingAliveCheckResponse = FALSE;
	DoIP_PthreadMutexUnlock();
}

/**********************************************************
 * 函数名称:	DoIP_ParameterConfig
 * 函数作用:	DoIP参数配置
 * 函数参数:	[in] tmp_doipconfig_param:		配置信息
 * 函数返回:	成功返回0，失败返回小于0
**********************************************************/
int DoIP_ParameterConfig(DOIP_CONFIG_PARAM_ST tmp_doipconfig_param)
{
	if (tmp_doipconfig_param.vin_len > 0)
	{
		sgSoAdDoip_cb.VinLen    = tmp_doipconfig_param.vin_len;
		sgSoAdDoip_cb.pVinValue = tmp_doipconfig_param.vin_val;
	}
	
	if (tmp_doipconfig_param.eid_len > 0)
	{
		sgSoAdDoip_cb.EidLen   = tmp_doipconfig_param.eid_len;
		sgSoAdDoip_cb.EidValue = tmp_doipconfig_param.eid_val;
	}
	
	if (tmp_doipconfig_param.gid_len > 0)
	{
		sgSoAdDoip_cb.GidLen   = tmp_doipconfig_param.gid_len;
		sgSoAdDoip_cb.GidValue = tmp_doipconfig_param.gid_val;
	}
	
	//FK_TRACE_INFO("sgSoAdDoip_cb.pVinValue: %s\n", sgSoAdDoip_cb.pVinValue);
	//FK_TRACE_INFO("sgSoAdDoip_cb.EidValue: 0x%012lX\n", sgSoAdDoip_cb.EidValue);
	//FK_TRACE_INFO("sgSoAdDoip_cb.GidValue: 0x%012lX\n", sgSoAdDoip_cb.GidValue);
	
	SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"pVinValue: %s,EidValue: 0x%012llX,GidValue: 0x%012llX\n", sgSoAdDoip_cb.pVinValue, sgSoAdDoip_cb.EidValue, sgSoAdDoip_cb.GidValue);
	sgSoAdDoip_cb.DoipTesterCnt				= tmp_doipconfig_param.tester_count;
	sgSoAdDoip_cb.DoipTargetCnt				= tmp_doipconfig_param.target_count;
	sgSoAdDoip_cb.DoipRoutingActivationCnt	= tmp_doipconfig_param.routingactivation_count;

	if (tmp_doipconfig_param.multicast_len > 0)
	{
		assert(tmp_doipconfig_param.multicast_len <= sizeof(gsoad_extcb.SoadMulticastAddr));
		memset(gsoad_extcb.SoadMulticastAddr, 0, sizeof(gsoad_extcb.SoadMulticastAddr));
		memcpy(gsoad_extcb.SoadMulticastAddr, tmp_doipconfig_param.multicast_address, tmp_doipconfig_param.multicast_len);
	}
	
	if (tmp_doipconfig_param.broadcast_len > 0)
	{
		assert(tmp_doipconfig_param.broadcast_len <= sizeof(gsoad_extcb.SoadBroadcastAddr));
		memset(gsoad_extcb.SoadBroadcastAddr, 0, sizeof(gsoad_extcb.SoadBroadcastAddr));
		memcpy(gsoad_extcb.SoadBroadcastAddr, tmp_doipconfig_param.broadcast_address, tmp_doipconfig_param.broadcast_len);
	}

	if (tmp_doipconfig_param.ethname_len > 0)
	{
		assert(tmp_doipconfig_param.ethname_len <= sizeof(gsoad_extcb.EthInterfaceName));
		memset(gsoad_extcb.EthInterfaceName, 0, sizeof(gsoad_extcb.EthInterfaceName));
		memcpy(gsoad_extcb.EthInterfaceName, tmp_doipconfig_param.ethname, tmp_doipconfig_param.ethname_len);
	}
	
	/** 根据TA最大个数分配最大空间，用于储存连接时TA的索引信息 **/
	if (sgSoAdDoip_cb.pTargetConnectionMap != NULL)
	{
		free(sgSoAdDoip_cb.pTargetConnectionMap);
		sgSoAdDoip_cb.pTargetConnectionMap = NULL;
	}
	sgSoAdDoip_cb.pTargetConnectionMap = (uint16 *)malloc(sgSoAdDoip_cb.DoipTargetCnt * sizeof(uint16));
	
	if (sgSoAdDoip_cb.pConnectionStatus != NULL)
	{
		free(sgSoAdDoip_cb.pConnectionStatus);
		sgSoAdDoip_cb.pConnectionStatus = NULL;
	}
	sgSoAdDoip_cb.pConnectionStatus = (DoIp_ArcDoIpSocketStatusType *)malloc(sgSoAdDoip_cb.DoipMaxTesterConnection * sizeof(DoIp_ArcDoIpSocketStatusType));

	return 0;
}

/**********************************************************
 * 函数名称:	DoIP_CallbackFUNConfig
 * 函数作用:	DoIP回调函数信息配置
 * 函数参数:	[in] tmp_doipconfig_funcallback:		回调函数信息结构体
 * 函数返回:	成功返回0，失败返回小于0
**********************************************************/
int DoIP_CallbackFUNConfig(DOIP_CONFIG_FUNCALLBACK_ST tmp_doipconfig_funcallback)
{
	sgSoAdDoip_cb.pDoIPServerDiagnosticMsg = tmp_doipconfig_funcallback.pDoIPServerDiagnosticMsg;
	gsoad_extcb.debuglevel = tmp_doipconfig_funcallback.debugstatus;
	gsoad_extcb.soaddoip_dmsg = tmp_doipconfig_funcallback.soaddoip_debugmsg;
	gsoad_extcb.DoipEventNotifier = tmp_doipconfig_funcallback.DoipEventNotifier;
	gsoad_extcb.SetDgsChnStatus = tmp_doipconfig_funcallback.SetDgsChnStatus;
	return 0;
}

/**********************************************************
 * 函数名称:	DoIP_Init
 * 函数作用:	DoIP初始化
 * 函数参数:	无
 * 函数返回:	成功返回0, 失败返回-1
**********************************************************/
int DoIP_Init(void)
{
	uint16 i;
	
#if 1
	/** 根据TA最大个数分配最大空间，用于储存连接时TA的索引信息 **/
	if (sgSoAdDoip_cb.pTargetConnectionMap != NULL)
	{
		free(sgSoAdDoip_cb.pTargetConnectionMap);
		sgSoAdDoip_cb.pTargetConnectionMap = NULL;
	}
	sgSoAdDoip_cb.pTargetConnectionMap = (uint16 *)malloc(sgSoAdDoip_cb.DoipTargetCnt * sizeof(uint16));
	
	if (sgSoAdDoip_cb.pConnectionStatus != NULL)
	{
		free(sgSoAdDoip_cb.pConnectionStatus);
		sgSoAdDoip_cb.pConnectionStatus = NULL;
	}
	sgSoAdDoip_cb.pConnectionStatus = (DoIp_ArcDoIpSocketStatusType *)malloc(sgSoAdDoip_cb.DoipMaxTesterConnection * sizeof(DoIp_ArcDoIpSocketStatusType));
#endif

	assert(pthread_mutex_init(&sgSoAdDoip_cb.MutexSend, NULL) == 0);

	for (i = 0; i < sgSoAdDoip_cb.DoipMaxTesterConnection; i++)
	{
		sgSoAdDoip_cb.pConnectionStatus[i].socketState					= DOIP_ARC_CONNECTION_INVALID;
		sgSoAdDoip_cb.pConnectionStatus[i].sockNr						= 0xff;
		sgSoAdDoip_cb.pConnectionStatus[i].sa							= 0xffff;
		sgSoAdDoip_cb.pConnectionStatus[i].activationType				= 0xff;
		sgSoAdDoip_cb.pConnectionStatus[i].awaitingAliveCheckResponse	= FALSE;
		sgSoAdDoip_cb.pConnectionStatus[i].generalInactivityTimer		= 0;		//5min, no data receive and sent before close socket
		sgSoAdDoip_cb.pConnectionStatus[i].initialInactivityTimer		= 0;		//2s
		sgSoAdDoip_cb.pConnectionStatus[i].aliveCheckTimer				= 0;		//500ms
	}

	return 0;
}

/**********************************************************
 * 函数名称:	DoIP_DeInit
 * 函数作用:	DoIP去初始化
 * 函数参数:	无
 * 函数返回:	成功返回0, 失败返回-1
**********************************************************/
int DoIP_DeInit(void)
{
#if 1	/** fly add,******** **/
	if (sgSoAdDoip_cb.pTargetConnectionMap != NULL)
	{
		free(sgSoAdDoip_cb.pTargetConnectionMap);
		sgSoAdDoip_cb.pTargetConnectionMap = NULL;
	}
	
	if (sgSoAdDoip_cb.pConnectionStatus != NULL)
	{
		free(sgSoAdDoip_cb.pConnectionStatus);
		sgSoAdDoip_cb.pConnectionStatus = NULL;
	}
#endif
	
	sgSoAdDoip_cb.NumAnnouncements = 0;
	assert(pthread_mutex_destroy(&sgSoAdDoip_cb.MutexSend) == 0);
	
	return 0;
}