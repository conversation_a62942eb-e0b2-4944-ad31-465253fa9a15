/*-------------------------------- Arctic Core ------------------------------
 * Copyright (C) 2013, ArcCore AB, Sweden, www.arccore.com.
 * Contact: <<EMAIL>>
 *
 * You may ONLY use this file:
 * 1)if you have a valid commercial ArcCore license and then in accordance with
 * the terms contained in the written license agreement between you and ArcCore,
 * or alternatively
 * 2)if you follow the terms found in GNU General Public License version 2 as
 * published by the Free Software Foundation and appearing in the file
 * LICENSE.GPL included in the packaging of this file or here
 * <http://www.gnu.org/licenses/old-licenses/gpl-2.0.txt>
 *-------------------------------- Arctic Core -----------------------------*/
#include <stdio.h>
#include <string.h>
#include <sys/select.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <errno.h>
#include <unistd.h>
#include <pthread.h>
#include <netinet/tcp.h>
#include <stddef.h>
#include <assert.h>
#include <sys/epoll.h>

#include "soad.h"
#include "soad_internal.h"

#if defined(USE_UDPNM)
#include "UdpNm.h"
#endif

#define STD_ON				0x01
#define STD_OFF				0x00
#define SOAD_DOIP_ACTIVE  	STD_ON

#define ETH_NAME			"eth0"					/* 默认网卡名称 */

typedef enum {
  SOAD_UNINITIALIZED = 0,
  SOAD_INITIALIZED
} SoadStateType;

#define MAX_EVENTS 10
typedef struct {
	int maxfdp1;
	int numfds;
	fd_set rset;
	int epollfd;
	struct epoll_event events[MAX_EVENTS];
} socketSelect_t;

typedef struct {
	boolean	bufferInUse;
	uint16	bufferLen;
	uint8*	bufferPtr;
} AdminBufferType;


typedef struct{
	ETH_CTRL_T 				EthSndCtrlCurrent;
	ETH_CTRL_T				EthRecvCtrlCurrent;
	socketSelect_t			SocketSelect;
	SoadStateType			ModuleStatus;
	SoadArcLinkStatusType	LinkStatus;
	int 						SoadExitFlag;   						/** 程序退出标识符,fly add,20220903 **/

	//SocketAdminType 				SocketAdminList[SOAD_SOCKET_COUNT];
	//PduAdminListType 				PduAdminList[SOAD_PDU_ROUTE_COUNT];
	pthread_t                       mainFunctionthread;                     /** 主线程 **/
	pthread_t                       mainTimerthread;                     /** 定时器线程 **/
}soad_cb_t;


static soad_cb_t sgsoad_cb = {
	.EthSndCtrlCurrent = ETH_APP_CTRL_NONE,
	.EthRecvCtrlCurrent = ETH_APP_CTRL_NONE,
	.ModuleStatus = SOAD_UNINITIALIZED,
	.LinkStatus = SOAD_ARC_LINKDOWN,
	.SoadExitFlag = 0,
};
	
soad_extcb_t gsoad_extcb = {
	.SoadMulticastAddr = MULTICAST_ADDRESS_ONE,
	.SoadBroadcastAddr = "***************",
	.EthInterfaceName = ETH_NAME,
	.debuglevel = SOADDOIP_DEBUG_NONE,
	.soaddoip_dmsg = NULL,
	.DoipEventNotifier = NULL,
	.SetDgsChnStatus = NULL,
};

static int SoAd_DeInit(void);

static uint8 gBuffer1[42];
static uint8 gBuffer2[SOAD_RX_BUFFER_SIZE];
static uint8 gBuffer3[SOAD_RX_BUFFER_SIZE];
static uint8 gBuffer4[SOAD_RX_BUFFER_SIZE];

#if 0
static uint8 gBuffer5[SOAD_RX_BUFFER_SIZE];
static uint8 gBuffer6[SOAD_RX_BUFFER_SIZE];
static uint8 gBuffer7[SOAD_RX_BUFFER_SIZE];
static uint8 gBuffer8[SOAD_RX_BUFFER_SIZE];
static uint8 gBuffer9[SOAD_RX_BUFFER_SIZE];
#define BUFFER_COUNT	9
#else
#define BUFFER_COUNT	4
#endif

static AdminBufferType gAdminBuffer[BUFFER_COUNT] = 
{
	{
		.bufferInUse = FALSE,
		.bufferLen = sizeof(gBuffer1),
		.bufferPtr = gBuffer1,
	},

	{
		.bufferInUse = FALSE,
		.bufferLen = sizeof(gBuffer2),
		.bufferPtr = gBuffer2,
	},

	{
		.bufferInUse = FALSE,
		.bufferLen = sizeof(gBuffer3),
		.bufferPtr = gBuffer3,
	},

	{
		.bufferInUse = FALSE,
		.bufferLen = sizeof(gBuffer4),
		.bufferPtr = gBuffer4,
	},

};

/**********************************************************
 * 函数名称:	SoAd_BufferFree
 * 函数作用:获取内存空间的指针
 * 函数参数:	[in] size：待分配的大小
 * 函数参数:	[in]buffPtr:		缓冲区指针
 * 函数返回:无
**********************************************************/
boolean SoAd_BufferGet(uint32 size, uint8** buffPtr)
{
	uint16 i;
	boolean returnCode = FALSE;

	DoIP_PthreadMutexLock();
	*buffPtr = NULL;
	for (i = 0; i < BUFFER_COUNT; i++)
	{
		if ((!gAdminBuffer[i].bufferInUse) && (gAdminBuffer[i].bufferLen >= size))
		{
			gAdminBuffer[i].bufferInUse = TRUE;
			*buffPtr = gAdminBuffer[i].bufferPtr;
			returnCode = TRUE;
			break;
		}
	}
	DoIP_PthreadMutexUnlock();

	return returnCode;
}

/**********************************************************
 * 函数名称:	SoAd_BufferFree
 * 函数作用:释放内存空间的指针
 * 函数参数:	[in] buffPtr:		缓冲区指针
 * 函数返回:无
**********************************************************/
void SoAd_BufferFree(uint8* buffPtr)
{
	uint16 i;
	uint16 j;

	DoIP_PthreadMutexLock();
	for (i = 0; i < BUFFER_COUNT; i++)
	{
		if (gAdminBuffer[i].bufferPtr == buffPtr)
		{
			gAdminBuffer[i].bufferInUse = FALSE;

			for (j = 0; j < gAdminBuffer[i].bufferLen; j++)
			{
				gAdminBuffer[i].bufferPtr[j] = 0xaa;
			}
			break;
		}
	}
	DoIP_PthreadMutexUnlock();
}

/**********************************************************
 * 函数名称:	SoAd_SocketClose
 * 函数作用:关闭以太网连接
 * 函数参数:	[in] sockNr:		存储句柄信息结构体数组索引
 * 函数返回:无
**********************************************************/
void SoAd_SocketClose(uint16 sockNr)
{
	uint16 i,release_i = 0,NoRelease_fg = 0;

	//FK_TRACE_INFO("SoAd_SocketClose, sockNr: %d, SocketState: %d\n", sockNr, gsoad_extcb.SocketAdminList[sockNr].SocketState);
	SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"SoAd_SocketClose, sockNr: %d, SocketState: %d,maxfdp1 = %d,SocketHandle = %d,ConnectionHandle = %d\n", \
		sockNr, gsoad_extcb.SocketAdminList[sockNr].SocketState,sgsoad_cb.SocketSelect.maxfdp1,gsoad_extcb.SocketAdminList[sockNr].SocketHandle,gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle);
	
	switch (gsoad_extcb.SocketAdminList[sockNr].SocketState)
	{
		case SOCKET_UDP_READY:
		case SOCKET_TCP_LISTENING:
		{
			shutdown(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, SHUT_RDWR);
			#if 0
			FD_CLR(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, &sgsoad_cb.SocketSelect.rset);
			if (gsoad_extcb.SocketAdminList[sockNr].SocketHandle == sgsoad_cb.SocketSelect.maxfdp1) {
		        sgsoad_cb.SocketSelect.maxfdp1 = -1;
		        // 遍历所有可能的fd，检查是否在集合中
		        for (i = 0; i < FD_SETSIZE; i++) {
		            if (FD_ISSET(i, &sgsoad_cb.SocketSelect.rset)) { // 这里仅检查读集合
		                if (i > sgsoad_cb.SocketSelect.maxfdp1) {
		                    sgsoad_cb.SocketSelect.maxfdp1 = i;
		                }
						SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"allFD_SET1,fdp1 = %d\n",i);
		            }
		        }
		    }
			#endif
			#if 1
			struct epoll_event event;
			event.events = EPOLLIN;  // 监听可读事件（可选 EPOLLOUT、EPOLLET 等）
			event.data.fd = gsoad_extcb.SocketAdminList[sockNr].SocketHandle;  // 关联的文件描述符
			if (epoll_ctl(sgsoad_cb.SocketSelect.epollfd, EPOLL_CTL_DEL, gsoad_extcb.SocketAdminList[sockNr].SocketHandle, &event) == -1) {
				perror("epoll_ctl_4");
				close(sgsoad_cb.SocketSelect.epollfd);
				//exit(EXIT_FAILURE);
			}
			#endif

			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"1,maxfdp1 = %d\n",sgsoad_cb.SocketSelect.maxfdp1);
			close(gsoad_extcb.SocketAdminList[sockNr].SocketHandle);
			gsoad_extcb.SocketAdminList[sockNr].SocketHandle = -1;
			gsoad_extcb.SocketAdminList[sockNr].SocketState = SOCKET_INIT;
			
			break;
		}
		case SOCKET_TCP_READY:
		{
			//FK_TRACE_INFO("SoAd_SocketClose\r\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"SoAd_SocketClose\n");
			
			shutdown(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, SHUT_RDWR);
			#if 0
			FD_CLR(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, &sgsoad_cb.SocketSelect.rset);
			if (gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle == sgsoad_cb.SocketSelect.maxfdp1) {
		        sgsoad_cb.SocketSelect.maxfdp1 = -1;
		        // 遍历所有可能的fd，检查是否在集合中
		        for (i = 0; i < FD_SETSIZE; i++) {
		            if (FD_ISSET(i, &sgsoad_cb.SocketSelect.rset)) { // 这里仅检查读集合
		                if (i > sgsoad_cb.SocketSelect.maxfdp1) {
		                    sgsoad_cb.SocketSelect.maxfdp1 = i;
		                }
						SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"allFD_SET2,fdp1 = %d\n",i);
		            }
		        }
		    }
			#endif
			#if 1
			struct epoll_event event;
			event.events = EPOLLIN;  // 监听可读事件（可选 EPOLLOUT、EPOLLET 等）
			event.data.fd = gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle;  // 关联的文件描述符
			if (epoll_ctl(sgsoad_cb.SocketSelect.epollfd, EPOLL_CTL_DEL, gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, &event) == -1) {
				perror("epoll_ctl_5");
				close(sgsoad_cb.SocketSelect.epollfd);
				//exit(EXIT_FAILURE);
			}
			#endif
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"2,maxfdp1 = %d\n",sgsoad_cb.SocketSelect.maxfdp1);
			close(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle);

	    	gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle = -1;
			gsoad_extcb.SocketAdminList[sockNr].TcpinitialInactivityTimer = 0;
			gsoad_extcb.SocketAdminList[sockNr].RemoteIpAddress = inet_addr(gSoAdConfig.SocketConnection[sockNr].SocketRemoteIpAddress);
			gsoad_extcb.SocketAdminList[sockNr].RemotePort = htons(gSoAdConfig.SocketConnection[sockNr].SocketRemotePort);

			gsoad_extcb.SocketAdminList[sockNr].SocketState = SOCKET_TCP_LISTENING;
			for (i = 0; i < SOAD_SOCKET_COUNT; i++){
				if (i == sockNr){
					continue;
				}

				if (gsoad_extcb.SocketAdminList[sockNr].SocketHandle == gsoad_extcb.SocketAdminList[i].SocketHandle){
					if (gsoad_extcb.SocketAdminList[i].SocketState == SOCKET_TCP_LISTENING){
						gsoad_extcb.SocketAdminList[sockNr].SocketState = SOCKET_DUPLICATE;
						break;
					}
				}
			}

			/* 与诊断仪断开连接时, 通知上层释放诊断通道 */
			if (gsoad_extcb.SocketAdminList[sockNr].SocketConnectionRef->SocketLocalPort == 13400)
			{
				/* 只有tcp通道均显示为释放，才最终释放以太网通道 */
				for(release_i=0;release_i < SOAD_SOCKET_COUNT;release_i++){
					if((gsoad_extcb.SocketAdminList[release_i].SocketProtocolIsTcp) && (gsoad_extcb.SocketAdminList[release_i].SocketState == SOCKET_TCP_READY)){
						NoRelease_fg = 1;
					}
				}
				if(NoRelease_fg == 0){
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"channel reset\n");
					if(gsoad_extcb.SetDgsChnStatus != NULL){
						gsoad_extcb.SetDgsChnStatus(0x02);
					}
				}
			}

			break;
		}
		default:
		{
			/* This should never happen! */
			//FK_TRACE_ERROR("SOAD_SOCKET_CLOSE_ID SOAD_E_SHALL_NOT_HAPPEN\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_SOCKET_CLOSE_ID SOAD_E_SHALL_NOT_HAPPEN\n");
			break;
		}
	}
}

/**********************************************************
 * 函数名称:	SoAd_SocketStatusCheck
 * 函数作用:套接字状态检查
 * 函数参数:	[in] sockNr:		存储句柄信息结构体数组索引
 * 函数参数:[in] sockHandle:		句柄信息
 * 函数返回:无
**********************************************************/
void SoAd_SocketStatusCheck(uint16 sockNr, int sockHandle)
{
	int sockErr;
	socklen_t sockErrLen = sizeof(sockErr);

	getsockopt(sockHandle, SOL_SOCKET, SO_ERROR, &sockErr, &sockErrLen);
	if ((sockErr != 0) && (sockErr != EWOULDBLOCK))
	{
		//FK_TRACE_ERROR("error: %d\r\n", sockErr);
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"error: %d\r\n", sockErr);
		DoIP_PthreadMutexLock();
		SoAd_SocketClose(sockNr);
		DoIP_PthreadMutexUnlock();
	}
	
	if (sockErr == ENOMEM)
	{
		//FK_TRACE_ERROR("DOIP_E_OUT_OF_MEMORY, error:%d\r\n", sockErr);
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"DOIP_E_OUT_OF_MEMORY, error:%d\r\n", sockErr);
		DoIP_createAndSendNack(sockNr, DOIP_E_OUT_OF_MEMORY);
	}
	
	//FK_TRACE_ERROR("error: %d\r\n", sockErr);
	//SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"error: %d\r\n", sockErr);

}

#if 0
/* 发送广播 */
static int broadcastTest(void)
{
	int brdcFd;
	int sendBytes;
	int optval = 1;
	struct sockaddr_in theirAddr;
	char msg[128] = "I am broadCast message from server!";

	if ((brdcFd = socket(PF_INET, SOCK_DGRAM, 0)) == -1)
	{
		FK_TRACE_ERROR("socket fail\n");
		return -1;
	}

	setsockopt(brdcFd, SOL_SOCKET, SO_BROADCAST | SO_REUSEADDR, &optval, sizeof(int));

	memset(&theirAddr, 0, sizeof(struct sockaddr_in));
	theirAddr.sin_family = AF_INET;
	theirAddr.sin_addr.s_addr = inet_addr("***************");
	theirAddr.sin_port = htons(13400);

	if ((sendBytes = sendto(brdcFd, msg, strlen(msg), 0, (struct sockaddr *)&theirAddr, sizeof(struct sockaddr))) == -1)
	{
		FK_TRACE_ERROR("sendto fail, errno=%d\n", errno);
		return -1;
	}

	close(brdcFd);
	
	return 0;
}
#endif

/**********************************************************
 * 函数名称:	SoAd_Comm_snd_Ctrl_Get
 * 函数作用:获取当前以太网通道的发送状态
 * 函数参数:无
 * 函数返回:返回当前发送状态值
**********************************************************/
ETH_CTRL_T SoAd_Comm_snd_Ctrl_Get(void)
{
	return sgsoad_cb.EthSndCtrlCurrent;
}

/**********************************************************
 * 函数名称:	SoAd_Comm_recv_Ctrl_Get
 * 函数作用:获取当前以太网通道的接收状态
 * 函数参数:无
 * 函数返回:返回当前接收状态值
**********************************************************/
ETH_CTRL_T SoAd_Comm_recv_Ctrl_Get(void)
{
	return sgsoad_cb.EthRecvCtrlCurrent;
}

/**********************************************************
 * 函数名称:	SoAd_Comm_Ctrl_Set
 * 函数作用:设置当前以太网通道的收发状态
 * 函数参数:	[in] eth_snd_ctrl_value:		发送状态值
 * 函数参数:	[in] eth_recv_ctrl_value:		接收状态值
 * 函数返回:无
**********************************************************/
void SoAd_Comm_Ctrl_Set(ETH_CTRL_T eth_snd_ctrl_value, ETH_CTRL_T eth_recv_ctrl_value)
{
	sgsoad_cb.EthSndCtrlCurrent = eth_snd_ctrl_value;
	sgsoad_cb.EthRecvCtrlCurrent = eth_recv_ctrl_value;
	// FK_TRACE_INFO("sgsoad_cb.EthSndCtrlCurrent: %d, sgsoad_cb.EthRecvCtrlCurrent: %d\r\n", sgsoad_cb.EthSndCtrlCurrent, sgsoad_cb.EthRecvCtrlCurrent);
}

/**********************************************************
 * 函数名称:	sendTcpMessage
 * 函数作用:	SoAd层发送TCP消息
 * 函数参数:	[in] sockNr:		存储链接句柄信息结构体数组索引
 * 函数参数:	[in] msgLen:		待发送的数据长度
 * 函数参数:	[in] buff:		待发送的数据缓冲区
 * 函数参数:	[in] pSocketAdminCur:		当前链接套接字的管理器
 * 函数返回:成功返回已经发送的字节数，失败返回0
**********************************************************/
static uint16 sendTcpMessage(uint16 sockNr, uint32 msgLen, uint8* buff, SocketAdminType *pSocketAdminCur)
{
	int bytesSent;

	if(pSocketAdminCur == NULL){
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"assert fail\n");
		assert(pSocketAdminCur != NULL);
	}
	
	bytesSent = send(pSocketAdminCur->ConnectionHandle, buff, msgLen, 0);
	if (bytesSent < 0)
	{
		//FK_TRACE_ERROR("tcp send message, error: %s(errno: %d)\n", strerror(errno), errno);
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"tcp send message, error: %s(errno: %d)\n", strerror(errno), errno);
		bytesSent = 0;
	}

	return bytesSent;
}

/**********************************************************
 * 函数名称:	sendUdpMessage
 * 函数作用:	SoAd层发送UDP消息
 * 函数参数:	[in] sockNr:		存储链接句柄信息结构体数组索引
 * 函数参数:	[in] msgLen:		待发送的数据长度
 * 函数参数:	[in] buff:		待发送的数据缓冲区
 * 函数参数:	[in] annonce_flag:		待发送的消息类型，1：表示全局广播，0：表示指定网段广播
 * 函数参数:	[in] pSocketAdminCur:		当前链接套接字的管理器
 * 函数返回:成功返回已经发送的字节数，失败返回0
**********************************************************/
static uint16 sendUdpMessage(uint16 sockNr, uint32 msgLen, uint8* buff, uint8 annonceFlag, SocketAdminType *pSocketAdminCur)
{
	int bytesSent;
	socklen_t toAddrLen;
	struct sockaddr_in toAddr;

	if(pSocketAdminCur == NULL){
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"assert fail\n");
		assert(pSocketAdminCur != NULL);
	}
		
	toAddrLen = sizeof(toAddr);
	toAddr.sin_family = AF_INET;
	// toAddr.sin_len = sizeof(toAddr);

	if (annonceFlag == 1)
	{
		//FK_TRACE_INFO("annonceFlag == 1\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"annonceFlag == 1\n");
		if (memcmp(gsoad_extcb.SoadBroadcastAddr, "***************", strlen("***************")) == 0)
		{
			toAddr.sin_addr.s_addr = htonl(INADDR_BROADCAST);
		}
		else
		{
			toAddr.sin_addr.s_addr = inet_addr(gsoad_extcb.SoadBroadcastAddr);
		}

		toAddr.sin_port = htons(13400);
	}
	else
	{
		//FK_TRACE_INFO("annonceFlag == 0\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"annonceFlag == 0\n");
		toAddr.sin_port = pSocketAdminCur->RemotePort;
		toAddr.sin_addr.s_addr = pSocketAdminCur->RemoteIpAddress;
	}
	#if 0
	FK_TRACE_INFO("sockNr: %d, SocketHandle: %d, RemoteIpAddress: %s, RemotePort: %d\n", \
			sockNr,
			pSocketAdminCur->SocketHandle,
			inet_ntoa(toAddr.sin_addr),
			ntohs(toAddr.sin_port));
	#endif
	SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,	"sockNr: %d, SocketHandle: %d, RemoteIpAddress: %s, RemotePort: %d\n", \
			sockNr,
			pSocketAdminCur->SocketHandle,
			inet_ntoa(toAddr.sin_addr),
			ntohs(toAddr.sin_port));
	bytesSent = sendto(pSocketAdminCur->SocketHandle, buff, msgLen, 0, (struct sockaddr *)&toAddr, toAddrLen);
	if (bytesSent < 0)
	{
		//FK_TRACE_ERROR("udp send message, error: %s(errno: %d)\n", strerror(errno), errno);
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"udp send message, error: %s(errno: %d)\n", strerror(errno), errno);
		bytesSent = 0;
	}

	return bytesSent;
}

/**********************************************************
 * 函数名称:	SoAd_SendIpMessage
 * 函数作用:	SoAd层发送以太网数据
 * 函数参数:	[in] sockNr:		存储链接句柄信息结构体数组索引
 * 函数参数:	[in] msgLen:		待发送的数据长度
 * 函数参数:	[in] buff:		待发送的数据缓冲区
 * 函数参数:	[in] annonce_flag:		待发送的消息类型，1：表示全局广播，0：表示指定网段广播
 * 函数返回:成功返回已经发送的字节数，失败返回0
**********************************************************/
uint16 SoAd_SendIpMessage(uint16 sockNr, uint32 msgLen, uint8* buff, uint8 annonce_flag)
{
	int bytesSent;
	ETH_CTRL_T eth_ctr;
	SocketAdminType SocketAdmincurrent;

	eth_ctr = SoAd_Comm_snd_Ctrl_Get();
	memset(&SocketAdmincurrent, 0, sizeof(SocketAdminType));
	
	switch (eth_ctr)
	{
		case ETH_APP_CTRL_SND_OFF:
		{
			if ((gSoAdConfig.SocketConnection[sockNr].AutosarConnectorType == SOAD_AUTOSAR_CONNECTOR_PDUR) \
					|| gSoAdConfig.SocketConnection[sockNr].AutosarConnectorType == SOAD_AUTOSAR_CONNECTOR_CDD)
			{
				return 0;
			}
			break;
		}
		case ETH_NETM_CTRL_SND_OFF:
		{
			if (gSoAdConfig.SocketConnection[sockNr].AutosarConnectorType == SOAD_AUTOSAR_CONNECTOR_UDPNM)
			{
				return 0;
			}
			break;
		}
		case ETH_ALL_CTRL_SND_OFF:
		{
			if ((gSoAdConfig.SocketConnection[sockNr].AutosarConnectorType == SOAD_AUTOSAR_CONNECTOR_UDPNM) \
					|| (gSoAdConfig.SocketConnection[sockNr].AutosarConnectorType == SOAD_AUTOSAR_CONNECTOR_PDUR))
			{
				return 0;
			}
			break;
		}
		default:
		{
			break;
		}
	}

	DoIP_PthreadMutexLock();
	memcpy(&SocketAdmincurrent, &gsoad_extcb.SocketAdminList[sockNr], sizeof(SocketAdminType));
	DoIP_PthreadMutexUnlock();

	if (SocketAdmincurrent.SocketProtocolIsTcp)
	{
		bytesSent = sendTcpMessage(sockNr, msgLen, buff, &SocketAdmincurrent);
	}
	else
	{
		bytesSent = sendUdpMessage(sockNr, msgLen, buff, annonce_flag, &SocketAdmincurrent);
	}

	return bytesSent;
}

/**********************************************************
 * 函数名称:	addRouteList
 * 函数作用:	增加IP路由列表信息
 * 函数参数:	[in] pRouteIpAddr:		待路由的IP地址
 * 函数参数:	[in] pEthName:		网卡信息
 * 函数返回:成功返回TRUE,失败返回FALSE
**********************************************************/
static boolean addRouteList(char_t *pRouteIpAddr, char_t *pEthName)
{
	boolean result;
	FILE *  pFileFp;
	char_t    line[128];
	char_t    tmpStr[128];

	//assert((pRouteIpAddr != NULL) && (pEthName != NULL));
	if(pRouteIpAddr == NULL || pEthName == NULL){
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"assert fail\n");
		assert((pRouteIpAddr != NULL) && (pEthName != NULL));
	}
	
	/* 检查需要新增的路由在路由表中是否已经存在 */
	result = FALSE;
	memset(tmpStr, 0, sizeof(tmpStr));
	snprintf(tmpStr, sizeof(tmpStr), "route -n | grep \"%s\"", pRouteIpAddr);
	pFileFp = popen(tmpStr, "r");
	if (NULL != pFileFp)
	{
		memset(line, 0, sizeof(line));
		while (fgets(line, sizeof(line), pFileFp) != NULL)
		{
			if (memcmp(line, pRouteIpAddr, strlen(pRouteIpAddr)) == 0)
			{
				result = TRUE;
				pclose(pFileFp);
				return result;
			}
			memset(line, 0, sizeof(line));
		}
	}
	pclose(pFileFp);

	/* 新增路由并检查是否添加成功 */
	result = FALSE;
	memset(tmpStr, 0, sizeof(tmpStr));
	snprintf(tmpStr, sizeof(tmpStr), "route add %s dev %s && route -n | grep \"%s\"", pRouteIpAddr, pEthName, pRouteIpAddr);
	pFileFp = popen(tmpStr, "r");
	if (NULL != pFileFp)
	{
		memset(line, 0, sizeof(line));
		while (fgets(line, sizeof(line), pFileFp) != NULL)
		{
			if (memcmp(line, pRouteIpAddr, strlen(pRouteIpAddr)) == 0)
			{
				result = TRUE;
				break;
			}
			memset(line, 0, sizeof(line));
		}
	}
	pclose(pFileFp);

	return result;
}

/**********************************************************
 * 函数名称:	socketCreate
 * 函数作用:	创建套接字
 * 函数参数:	[in] sockNr:		存储套接字结构体索引
 * 函数返回:无
**********************************************************/
static void socketCreate(uint16 sockNr)
{
	int i;
	int on;
	int ret;
    int sockFd;
    int sockType;
    struct sockaddr_in sLocalAddr;

    if (gsoad_extcb.SocketAdminList[sockNr].SocketProtocolIsTcp)
	{
    	sockType = SOCK_STREAM;
    }
	else
	{
    	sockType = SOCK_DGRAM;
    }

    sockFd = socket(AF_INET, sockType, 0);
    if (sockFd >= 0)
	{
		on = 1;
    	memset((char *)&sLocalAddr, 0, sizeof(sLocalAddr));

		ioctl(sockFd, FIONBIO, &on);			//set none block socket
		
		if (sockType == SOCK_STREAM)
		{
			ret = setsockopt(sockFd, IPPROTO_TCP, TCP_NODELAY, &on, sizeof(int));		// Set socket to no delay
			if (ret != 0)
			{
			    //FK_TRACE_ERROR("setsockopt TCP_NODELAY error: %d, %s\n", errno, strerror(errno));
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"setsockopt TCP_NODELAY error: %d, %s\n", errno, strerror(errno));
			    close(sockFd);
			    return;
			}
			
			ret = setsockopt(sockFd, SOL_SOCKET, SO_REUSEADDR, &on, sizeof(int));		// Set socket to reuse addr
			if (ret != 0)
			{
			    //FK_TRACE_ERROR("setsockopt SO_REUSEADDR error: %d, %s\n", errno, strerror(errno));
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"setsockopt SO_REUSEADDR error: %d, %s\n", errno, strerror(errno));
			    close(sockFd);
			    return;
			}
		}
		else
		{
			addRouteList(gsoad_extcb.SoadMulticastAddr, gsoad_extcb.EthInterfaceName);
			addRouteList(gsoad_extcb.SoadBroadcastAddr, gsoad_extcb.EthInterfaceName);

			ret = setsockopt(sockFd, SOL_SOCKET, SO_BROADCAST, &on, sizeof(int));		// Set socket to broadcast on
			if (ret != 0)
			{
			    //FK_TRACE_ERROR("setsockopt SO_BROADCAST error: %d, %s\n", errno, strerror(errno));
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"setsockopt SO_BROADCAST error: %d, %s\n", errno, strerror(errno));
			    close(sockFd);
			    return;
			}
			
			ret = setsockopt(sockFd, SOL_SOCKET, SO_REUSEADDR, &on, sizeof(int));		// Set socket to reuse addr
			if (ret != 0)
			{
			    //FK_TRACE_ERROR("setsockopt SO_REUSEADDR error: %d, %s\n", errno, strerror(errno));
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"setsockopt SO_REUSEADDR error: %d, %s\n", errno, strerror(errno));
			    close(sockFd);
			    return;
			}
		}

    	sLocalAddr.sin_family = AF_INET;
    	//sLocalAddr.sin_len = sizeof(sLocalAddr);
    	sLocalAddr.sin_addr.s_addr = htonl(INADDR_ANY);		// TODO: Use IP from configuration instead 127.0.0.1 for test
    	sLocalAddr.sin_port = htons(gsoad_extcb.SocketAdminList[sockNr].SocketConnectionRef->SocketLocalPort);

		if ((ret = bind(sockFd, (struct sockaddr *)&sLocalAddr, sizeof(sLocalAddr))) >= 0)
		{
			if (!gsoad_extcb.SocketAdminList[sockNr].SocketProtocolIsTcp)
			{
				struct ip_mreq mreq;			// multicast
				
				mreq.imr_multiaddr.s_addr = inet_addr(gsoad_extcb.SoadMulticastAddr);			//inet_addr(MULTICAST_ADDRESS_ONE);
				mreq.imr_interface.s_addr = htonl(INADDR_ANY);
				
				ret = setsockopt(sockFd, IPPROTO_IP, IP_ADD_MEMBERSHIP, &mreq, sizeof(mreq));
				if (ret < 0)
				{
					#if 0
					FK_TRACE_ERROR("setsockopt add multicast failed: %s(errno:%d), multicast address: %s\r\n", \
							strerror(errno), errno, gsoad_extcb.SoadMulticastAddr);
					#endif
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"setsockopt add multicast failed: %s(errno:%d), multicast address: %s\r\n", \
							strerror(errno), errno, gsoad_extcb.SoadMulticastAddr);
					// return;
				}
				else
				{
					//FK_TRACE_INFO("setsockopt add multicast(%s) success\n", gsoad_extcb.SoadMulticastAddr);
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"setsockopt add multicast(%s) success\n", gsoad_extcb.SoadMulticastAddr);
				}
            	
				// Now the UDP socket is ready for receive/transmit
            	gsoad_extcb.SocketAdminList[sockNr].SocketHandle = sockFd;
            	gsoad_extcb.SocketAdminList[sockNr].SocketState = SOCKET_UDP_READY;
				FD_SET(sockFd, &sgsoad_cb.SocketSelect.rset);
				if (sgsoad_cb.SocketSelect.maxfdp1 < sockFd)
				{
					sgsoad_cb.SocketSelect.maxfdp1 = sockFd;
				}
				sgsoad_cb.SocketSelect.numfds++;
				
				#if 1
				struct epoll_event event;
				event.events = EPOLLIN;  // 监听可读事件（可选 EPOLLOUT、EPOLLET 等）
				event.data.fd = sockFd;  // 关联的文件描述符

				// 操作类型：
				// - EPOLL_CTL_ADD：添加监听
				// - EPOLL_CTL_MOD：修改监听事件
				// - EPOLL_CTL_DEL：移除监听
				if (epoll_ctl(sgsoad_cb.SocketSelect.epollfd, EPOLL_CTL_ADD, sockFd, &event) == -1) {
					perror("epoll_ctl_1");
					close(sgsoad_cb.SocketSelect.epollfd);
					//exit(EXIT_FAILURE);
				}
				#endif

				//FK_TRACE_INFO("udp create, sockNr: %d, sockFd: %d, maxfd: %d\n", sockNr, sockFd, sgsoad_cb.SocketSelect.maxfdp1);
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"udp create, sockNr: %d, sockFd: %d, maxfd: %d\n", sockNr, sockFd, sgsoad_cb.SocketSelect.maxfdp1);

            }
			else
			{
				SoAd_Cbk_LocalIpAssignmentChg(0, true, NULL);			/* 发送车辆声明 */
                
				if (listen(sockFd, 20) == 0)
				{
                	// Now the TCP socket is ready for receive/transmit
                	gsoad_extcb.SocketAdminList[sockNr].SocketHandle = sockFd;
                	gsoad_extcb.SocketAdminList[sockNr].SocketState = SOCKET_TCP_LISTENING;
					for (i = 0; i < SOAD_SOCKET_COUNT; i++)
					{
						if (gsoad_extcb.SocketAdminList[i].SocketProtocolIsTcp)
						{
							gsoad_extcb.SocketAdminList[i].SocketHandle = sockFd;
                			gsoad_extcb.SocketAdminList[i].SocketState = SOCKET_TCP_LISTENING;
						}
					}
					
					FD_SET(sockFd, &sgsoad_cb.SocketSelect.rset);
					if (sgsoad_cb.SocketSelect.maxfdp1 < sockFd)
					{
						sgsoad_cb.SocketSelect.maxfdp1 = sockFd;
					}
					sgsoad_cb.SocketSelect.numfds++;
					
					#if 1
					struct epoll_event event;
					event.events = EPOLLIN;  // 监听可读事件（可选 EPOLLOUT、EPOLLET 等）
					event.data.fd = sockFd;  // 关联的文件描述符

					// 操作类型：
					// - EPOLL_CTL_ADD：添加监听
					// - EPOLL_CTL_MOD：修改监听事件
					// - EPOLL_CTL_DEL：移除监听
					if (epoll_ctl(sgsoad_cb.SocketSelect.epollfd, EPOLL_CTL_ADD, sockFd, &event) == -1) {
						perror("epoll_ctl_2");
						close(sgsoad_cb.SocketSelect.epollfd);
						//exit(EXIT_FAILURE);
					}
					#endif

					//FK_TRACE_INFO("tcp create, sockNr: %d, sockFd: %d, maxfd: %d\n", sockNr, sockFd, sgsoad_cb.SocketSelect.maxfdp1);
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"tcp create, sockNr: %d, sockFd: %d, maxfd: %d\n", sockNr, sockFd, sgsoad_cb.SocketSelect.maxfdp1);
                }
				else
				{
					//FK_TRACE_INFO("listen fail\n");
					SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"listen fail\n");
                	close(sockFd);
                }
            }
    	}
		else
		{
			//FK_TRACE_INFO("Bind fail, sockFd: %d, sin_port: %d\n", sockFd, ntohs(sLocalAddr.sin_port));
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"Bind fail, sockFd: %d, sin_port: %d,ret = %d,errno = %d\n", sockFd, ntohs(sLocalAddr.sin_port),ret,errno);
			close(sockFd);
    	}
    }
	else
	{
    	// Socket creation failed
    	// Do nothing, try again later
    	//FK_TRACE_WARN("Socket creation failed\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_WARN,"Socket creation failed\n");
    }
}

/**********************************************************
 * 函数名称:	socketAccept
 * 函数作用:	等待套接字连接
 * 函数参数:	[in] sockNr:		存储套接字结构体索引
 * 函数返回:无
**********************************************************/
static void socketAccept(uint16 sockNr)
{
	uint16 i;
	int clientFd;
	struct sockaddr_in client_addr;
	int addrlen = sizeof(client_addr);

	clientFd = accept(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, (struct sockaddr*)&client_addr, (socklen_t *)&addrlen);
	SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"tcp accept, sockNr: %d, clientFd: %d,SocketHandle = %d\n", sockNr, clientFd,gsoad_extcb.SocketAdminList[sockNr].SocketHandle);
	if (clientFd >= 0 && gsoad_extcb.SocketAdminList[sockNr].SocketConnectionRef->SocketLocalPort == 13400)
	{
		if (gsoad_extcb.SetDgsChnStatus != NULL && gsoad_extcb.SetDgsChnStatus(0x01) < 0)			/* 若当前不允许诊断，则不允许以太网诊断仪接入 */
		{
			close(clientFd);
			clientFd = -1;
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_WARN,"tcp accept, sockNr: %d, SetDgsChnStatus: %d\n", sockNr, gsoad_extcb.SetDgsChnStatus(0x01));
			return;
		}
	}

	if (clientFd != (-1))
	{
		// Check that remote port and ip match
		// TODO: Check remote port and ip with gsoad_extcb.SocketAdminList and select first matching

		// New connection established
		int on = 1;
    	ioctl(clientFd, FIONBIO, &on);											// Set socket to non block mode
    	setsockopt(clientFd, IPPROTO_TCP, TCP_NODELAY, &on, sizeof(int));		// Set socket to no delay
		// setsockopt(clientFd, IPPROTO_TCP, TCP_QUICKACK, &on, sizeof(int));		// Set socket quick ack

    	gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle = clientFd;
		gsoad_extcb.SocketAdminList[sockNr].RemotePort = client_addr.sin_port;
		gsoad_extcb.SocketAdminList[sockNr].RemoteIpAddress = client_addr.sin_addr.s_addr;
		gsoad_extcb.SocketAdminList[sockNr].SocketState = SOCKET_TCP_READY;

		FD_SET(clientFd, &sgsoad_cb.SocketSelect.rset);
		if (sgsoad_cb.SocketSelect.maxfdp1 < clientFd)
		{
			sgsoad_cb.SocketSelect.maxfdp1 = clientFd;
		}

		#if 1
		struct epoll_event event;
		event.events = EPOLLIN;  // 监听可读事件（可选 EPOLLOUT、EPOLLET 等）
		event.data.fd = clientFd;  // 关联的文件描述符

		// 操作类型：
		// - EPOLL_CTL_ADD：添加监听
		// - EPOLL_CTL_MOD：修改监听事件
		// - EPOLL_CTL_DEL：移除监听
		if (epoll_ctl(sgsoad_cb.SocketSelect.epollfd, EPOLL_CTL_ADD, clientFd, &event) == -1) {
			perror("epoll_ctl_3");
			close(sgsoad_cb.SocketSelect.epollfd);
			//exit(EXIT_FAILURE);
		}
		#endif

		//FK_TRACE_INFO("tcp accept, sockNr: %d, clientFd: %d, maxfd: %d\r\n", sockNr, clientFd, sgsoad_cb.SocketSelect.maxfdp1);
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"tcp accept, sockNr: %d, clientFd: %d, maxfd: %d\r\n", sockNr, clientFd, sgsoad_cb.SocketSelect.maxfdp1);
		// Check if there is any free duplicate of this socket
		for (i = 0; i < SOAD_SOCKET_COUNT; i++)
		{
			if ((gsoad_extcb.SocketAdminList[i].SocketState == SOCKET_DUPLICATE) \
					&& (gSoAdConfig.SocketConnection[i].SocketProtocol == gSoAdConfig.SocketConnection[sockNr].SocketProtocol) \
					&& (gSoAdConfig.SocketConnection[i].SocketLocalPort == gSoAdConfig.SocketConnection[sockNr].SocketLocalPort))
			{
				// Yes, move the old socket to this
				gsoad_extcb.SocketAdminList[i].SocketHandle = gsoad_extcb.SocketAdminList[sockNr].SocketHandle;
				gsoad_extcb.SocketAdminList[i].SocketState = SOCKET_TCP_LISTENING;
				//FK_TRACE_INFO("infor: socket accept \r\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"infor: socket accept \r\n");
				// gsoad_extcb.SocketAdminList[sockNr].SocketHandle = -1;
				break;
			}
		}
	}
	else
	{
		// FK_TRACE_INFO("clientFd == -1\r\n");
	}
}

/**********************************************************
 * 函数名称:	SoAd_GetNofCurrentlyUsedTcpSockets
 * 函数作用:	获取当前建立连接的TCP套接字数量
 * 函数参数:无
 * 函数返回:返回建立的套接字个数
**********************************************************/
uint8 SoAd_GetNofCurrentlyUsedTcpSockets(void)
{
	uint16 i;
	uint8 count = 0;

	for (i = 0; i < SOAD_SOCKET_COUNT; i++)
	{
		if ((SOCKET_TCP_READY == gsoad_extcb.SocketAdminList[i].SocketState) && gsoad_extcb.SocketAdminList[i].SocketProtocolIsTcp)
		{
			count++;
		}
	}

	return count;
}

/**********************************************************
 * 函数名称:	SoAd_GetNofMaxUsedTcpSockets
 * 函数作用:	获取所有TCP套接字总数
 * 函数参数:无
 * 函数返回:返回TCP套接字个数
**********************************************************/
uint8 SoAd_GetNofMaxUsedTcpSockets(void)
{
	uint16 i;
	uint8 count = 0;

	for (i = 0; i < SOAD_SOCKET_COUNT; i++)
	{
		if (gsoad_extcb.SocketAdminList[i].SocketProtocolIsTcp)
		{
			count++;
		}
	}

	return count - 1;
}

/**********************************************************
 * 函数名称:	socketTcpRead
 * 函数作用:	读取tcp数据
 * 函数参数:[in] sockNr:存储句柄信息结构体数据的索引
 * 函数返回:无
**********************************************************/
static void socketTcpRead(uint16 sockNr)
{
	switch (gsoad_extcb.SocketAdminList[sockNr].SocketConnectionRef->AutosarConnectorType)
	{
		case SOAD_AUTOSAR_CONNECTOR_PDUR:
		{
			switch(SoAd_Comm_recv_Ctrl_Get())
			{
				case ETH_APP_CTRL_RECV_OFF:
				case ETH_ALL_CTRL_RECV_OFF:
				{
					return;
				}
				default:
				{
					break;
				}
			}

			break;
		}
		case SOAD_AUTOSAR_CONNECTOR_DOIP:
		{
			DoIP_HandleTcpRx(sockNr);
			break;
		}
		default:
		{
			//FK_TRACE_ERROR("SOAD_SOCKET_TCP_READ_ID SOAD_E_NOCONNECTOR \r\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_SOCKET_TCP_READ_ID SOAD_E_NOCONNECTOR \n");
			break;
		}
	}
}

#if defined(USE_UDPNM)
//not use pduroute ,use socket route instead
static void Udp_Nm_HandleUdpRx(uint16 sockNr)
{
	int nBytes;
	PduInfoType pduInfo;
	struct sockaddr_in fromAddr;
    socklen_t fromAddrLen = sizeof(fromAddr);
	
	if (SoAd_BufferGet(SOAD_RX_BUFFER_SIZE, &pduInfo.SduDataPtr))
	{
		nBytes = recvfrom(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, pduInfo.SduDataPtr, SOAD_RX_BUFFER_SIZE, MSG_PEEK, (struct sockaddr*)&fromAddr, &fromAddrLen);

		SoAd_SocketStatusCheck(sockNr, gsoad_extcb.SocketAdminList[sockNr].SocketHandle);

		if (nBytes > 0)
		{
			if(nBytes >= gsoad_extcb.SocketAdminList[sockNr].SocketRouteRef->DestinationSduLength)
			{
				// IF-type
				pduInfo.SduLength = recvfrom(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, pduInfo.SduDataPtr,  gsoad_extcb.SocketAdminList[sockNr].SocketRouteRef->DestinationSduLength, 0, (struct sockaddr*)&fromAddr, &fromAddrLen);

				gsoad_extcb.SocketAdminList[sockNr].RemotePort = fromAddr.sin_port;
				gsoad_extcb.SocketAdminList[sockNr].RemoteIpAddress = fromAddr.sin_addr.s_addr;
				
				/* TODO Find out how autosar connector and user really shall be used. This is just one interpretation
				 * support for XCP, CDD will have to be added later when supported */
				switch(SoAd_Comm_recv_Ctrl_Get())
				{
					case ETH_NETM_CTRL_RECV_OFF:
					case ETH_ALL_CTRL_RECV_OFF:
						return;
				}
				UdpNm_SoAdIfRxIndication(sockNr, pduInfo.SduDataPtr);
			}
		}

		SoAd_BufferFree(pduInfo.SduDataPtr);
	}
}
#endif

/**********************************************************
 * 函数名称:	socketUdpRead
 * 函数作用:	读取UDSP数据
 * 函数参数:[in] sockNr:存储句柄信息结构体数据的索引
 * 函数返回:无
**********************************************************/
static void socketUdpRead(uint16 sockNr)
{
	//FK_TRACE_INFO("socketUdpRead AutosarConnectorType:%d\r\n",gsoad_extcb.SocketAdminList[sockNr].SocketConnectionRef->AutosarConnectorType);
	
	switch (gsoad_extcb.SocketAdminList[sockNr].SocketConnectionRef->AutosarConnectorType)
	{
		case SOAD_AUTOSAR_CONNECTOR_PDUR:
		{
			break;
		}
#if defined(USE_UDPNM)
		case SOAD_AUTOSAR_CONNECTOR_UDPNM:
		{
			Udp_Nm_HandleUdpRx(sockNr);
			break;
		}
#endif
		case SOAD_AUTOSAR_CONNECTOR_DOIP:
		{
			DoIP_HandleUdpRx(sockNr);
			break;
		}
		default:
		{
			//FK_TRACE_ERROR("SOAD_SOCKET_UDP_READ_ID SOAD_E_NOCONNECTOR \r\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_SOCKET_UDP_READ_ID SOAD_E_NOCONNECTOR\n");
			break;
		}
	}
}

/**********************************************************
 * 函数名称:	scanSockets
 * 函数作用:	遍历套接字句柄信息
 * 函数参数:无
 * 函数返回:无
**********************************************************/
static void scanSockets(void)
{
	uint16 i;
	int nready = 0;
	struct timeval timeout;
	// static uint16 count = 0;
	static int timeoutcount = 0;
	
	if (gSoadRecvDebugLevel <= 0 && timeoutcount > 50)  /** 只是用来打印调试信息 **/
	{
		for (i = 0; i < SOAD_SOCKET_COUNT; i++)
		{
			#if 0	
			FK_TRACE_INFO("infor 0: scan sockets i: %d, SocketState: %d, SocketHandle: %d, ConnectionHandle: %d\n", \
					i, gsoad_extcb.SocketAdminList[i].SocketState, gsoad_extcb.SocketAdminList[i].SocketHandle, gsoad_extcb.SocketAdminList[i].ConnectionHandle);
			#endif
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"infor 0: scan sockets i: %d, SocketState: %d, SocketHandle: %d, ConnectionHandle: %d\n", \
					i, gsoad_extcb.SocketAdminList[i].SocketState, gsoad_extcb.SocketAdminList[i].SocketHandle, gsoad_extcb.SocketAdminList[i].ConnectionHandle);
		}
	}

	for (i = 0; i < SOAD_SOCKET_COUNT; i++)
	{
		if (0 <= 2 && timeoutcount > 50)
		{
			#if 0
			FK_TRACE_INFO("infor: scan sockets i: %d, SocketState: %d, SocketHandle: %d, ConnectionHandle: %d\n", \
					i, gsoad_extcb.SocketAdminList[i].SocketState, gsoad_extcb.SocketAdminList[i].SocketHandle, gsoad_extcb.SocketAdminList[i].ConnectionHandle);
			#endif
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"infor: scan sockets i: %d, SocketState: %d, SocketHandle: %d, ConnectionHandle: %d\n", \
					i, gsoad_extcb.SocketAdminList[i].SocketState, gsoad_extcb.SocketAdminList[i].SocketHandle, gsoad_extcb.SocketAdminList[i].ConnectionHandle);
		}

		switch (gsoad_extcb.SocketAdminList[i].SocketState)
		{
			case SOCKET_INIT:
			{
				socketCreate(i);
				
				/* if (count++ > 5000)
				{
					count = 0;
					FK_TRACE_INFO("infor: socket create i=%d \r\n",i);
				} */
				
				break;
			}
			case SOCKET_TCP_LISTENING:
			{
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"infor: socket TCP accept i=%d,sgsoad_cb.SocketSelect.maxfdp1 = %d \r\n",i,sgsoad_cb.SocketSelect.maxfdp1);

				socketAccept(i);
				#if 0
				if (sgsoad_cb.SocketSelect.maxfdp1 != 0 && gsoad_extcb.SocketAdminList[i].SocketHandle > 0)
				{
					if (FD_ISSET(gsoad_extcb.SocketAdminList[i].SocketHandle, &sgsoad_cb.SocketSelect.rset))
					{
						socketAccept(i);
					}
					FD_SET(gsoad_extcb.SocketAdminList[i].SocketHandle,&sgsoad_cb.SocketSelect.rset);
				}
				#endif
				/* if (count++ > 5000)
				{
					count = 0;
					FK_TRACE_INFO("infor: socket create i=%d \r\n",i);
				} */
				
				break;
			}
			case SOCKET_TCP_READY:
			{
				//FK_TRACE_INFO("infor: socket TCP read i=%d\r\n",i);
				#if 0
				if (sgsoad_cb.SocketSelect.maxfdp1 != 0 && gsoad_extcb.SocketAdminList[i].ConnectionHandle > 0)
				{
					if (FD_ISSET(gsoad_extcb.SocketAdminList[i].ConnectionHandle, &sgsoad_cb.SocketSelect.rset))
					{
						if (gSoadRecvDebugLevel <= 1){
							//FK_TRACE_INFO("SOCKET_TCP_READY......\r\n");
							SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"SOCKET_TCP_READY......\n");
						}
						socketTcpRead(i);
					}
					FD_SET(gsoad_extcb.SocketAdminList[i].ConnectionHandle,&sgsoad_cb.SocketSelect.rset);
					FD_SET(gsoad_extcb.SocketAdminList[i].SocketHandle,&sgsoad_cb.SocketSelect.rset);
				}
				#endif
				socketTcpRead(i);
				break;
			}
			case SOCKET_UDP_READY:
			{
				#if 0
				//FK_TRACE_INFO("infor: socket  udp read i=%d,%d\r\n",i,gsoad_extcb.SocketAdminList[i].SocketHandle);
				if (sgsoad_cb.SocketSelect.maxfdp1 != 0 && gsoad_extcb.SocketAdminList[i].SocketHandle > 0)
				{
					if (FD_ISSET(gsoad_extcb.SocketAdminList[i].SocketHandle, &sgsoad_cb.SocketSelect.rset))
					{
						if (gSoadRecvDebugLevel <= 1){
							//FK_TRACE_INFO("SOCKET_UDP_READY......\r\n");
							SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"SOCKET_UDP_READY......\n");
						}
						socketUdpRead(i);
						
					}
					FD_SET(gsoad_extcb.SocketAdminList[i].SocketHandle,&sgsoad_cb.SocketSelect.rset);
				}
				#endif
				socketUdpRead(i);
				break;
			}
			case SOCKET_DUPLICATE:
			{
				/* Do nothing */
				break;
			}
			default:
			{
				/* This should never happen! */
				//FK_TRACE_ERROR("SOAD_SCAN_SOCKETS_ID, SOAD_E_SHALL_NOT_HAPPEN \r\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_SCAN_SOCKETS_ID, SOAD_E_SHALL_NOT_HAPPEN\n");
				break;
			}
		} /* End of switch */
		
		if (gSoadRecvDebugLevel <= 1 && timeoutcount > 50)
		{
			#if 0
			FK_TRACE_INFO("infor: scan sockets i: %d, SocketState: %d, SocketHandle %d, ConnectionHandle: %d\n", \
					i, gsoad_extcb.SocketAdminList[i].SocketState, gsoad_extcb.SocketAdminList[i].SocketHandle, gsoad_extcb.SocketAdminList[i].ConnectionHandle);
			#endif
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"infor: scan sockets i: %d, SocketState: %d, SocketHandle %d, ConnectionHandle: %d\n", \
					i, gsoad_extcb.SocketAdminList[i].SocketState, gsoad_extcb.SocketAdminList[i].SocketHandle, gsoad_extcb.SocketAdminList[i].ConnectionHandle);
		}
	}

#if 1
  nready = epoll_wait(sgsoad_cb.SocketSelect.epollfd, sgsoad_cb.SocketSelect.events, MAX_EVENTS, -1);
  switch (nready)
	{
        case -1:
		{
            //FK_TRACE_ERROR("select error!\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"select error!\n");
            usleep(100*1000);
            /** 错误处理 **/
            break;
		}
        case 0:
		{
			if (gSoadRecvDebugLevel <= 1 && timeoutcount > 50)
			{
            	//FK_TRACE_INFO("select timeout!\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"select timeout!\n");
			}
			
			if (timeoutcount > 50)
			{
				timeoutcount = 0;
			}
			timeoutcount++;
			
			break;
		}
        default:
		{
            break;
		}
    }
#endif
#if 0
    timeout.tv_sec = 2;
    timeout.tv_usec = 200000;

	nready = select(sgsoad_cb.SocketSelect.maxfdp1 + 1, &sgsoad_cb.SocketSelect.rset, NULL, NULL, &timeout);

	switch (nready)
	{
        case -1:
		{
            //FK_TRACE_ERROR("select error!\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"select error!\n");
            usleep(100*1000);
            /** 错误处理 **/
            break;
		}
        case 0:
		{
			if (gSoadRecvDebugLevel <= 1 && timeoutcount > 50)
			{
            	//FK_TRACE_INFO("select timeout!\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"select timeout!\n");
			}
			
			if (timeoutcount > 50)
			{
				timeoutcount = 0;
			}
			timeoutcount++;
			
			break;
		}
        default:
		{
            break;
		}
    }
#endif
#if 0
	nready = select(sgsoad_cb.SocketSelect.maxfdp1+1, &sgsoad_cb.SocketSelect.rset, NULL, NULL, NULL);
#endif
}

#if 0
//instead PduR_ARC_RouteTransmit if not using pduroute
static void handleTx()
{
	uint8 i = 0; 

	//SoAdIf_Transmit(); //if route will use this
	//SoAdTp_Transmit(gDianosticBuffer.dianostic_pdutype, gDianosticBuffer.dianostic_pduinfo); // uds use this to send dianostic message

	for (i = 0; i < SOAD_SOCKET_COUNT; i++)
	{
		// DoipSendProcess(i);
	}
}
#endif

/** @req SOAD121 */
/**********************************************************
 * 函数名称:	mainFunctionThread
 * 函数作用:	主任务处理线程
 * 函数参数:[in] pdata :私有数据指针
 * 函数返回:无
**********************************************************/
static void *mainFunctionThread(void* pdata)
{
	FD_ZERO(&sgsoad_cb.SocketSelect.rset);
	#if 1
	sgsoad_cb.SocketSelect.epollfd = epoll_create1(0);  // 参数 flags 通常传 0
	if (sgsoad_cb.SocketSelect.epollfd == -1) {
		perror("doips epoll_create1");
	}
	#endif
	while (1){
		if (sgsoad_cb.SoadExitFlag == 2){
			break;
		}
		
		if (sgsoad_cb.ModuleStatus != SOAD_UNINITIALIZED){
			scanSockets();
			//handleTx();
		}
	}
	
	SoAd_DeInit();
	if (close(sgsoad_cb.SocketSelect.epollfd) == -1) {
        perror("doips epoll close");
    }
	return NULL;
}

/** @req SOAD093 */
/**********************************************************
 * 函数名称:	SoAd_Init
 * 函数作用:	socket adapter套接字适配器层信息初始化
 * 函数参数:无
 * 函数返回:无
**********************************************************/
static void SoAd_Init(void)
{
	uint16 i, j;
	// Initiate the socket administration list
	for (i = 0; i < SOAD_SOCKET_COUNT; i++)
	{
		gsoad_extcb.SocketAdminList[i].SocketNr = i;
		gsoad_extcb.SocketAdminList[i].SocketState = SOCKET_INIT;
		if ((gSoAdConfig.SocketConnection[i].SocketProtocol == SOAD_SOCKET_PROT_TCP)&&(gSoAdConfig.SocketConnection[i].SocketUdpListenOnly == false))
		{
			gsoad_extcb.SocketAdminList[i].SocketState = SOCKET_TCP_LISTENING;
		}
		
		gsoad_extcb.SocketAdminList[i].SocketConnectionRef = &gSoAdConfig.SocketConnection[i];
		if (strcmp(gSoAdConfig.SocketConnection[i].SocketRemoteIpAddress,"***************") == 0)
		{
			gsoad_extcb.SocketAdminList[i].RemoteIpAddress = htonl(INADDR_BROADCAST);
			//FK_TRACE_INFO("%d,0x%08x\r\n", i, gsoad_extcb.SocketAdminList[i].RemoteIpAddress);
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"%d,0x%08x\n", i, gsoad_extcb.SocketAdminList[i].RemoteIpAddress);
		}
		else
		{
			gsoad_extcb.SocketAdminList[i].RemoteIpAddress = inet_addr(gSoAdConfig.SocketConnection[i].SocketRemoteIpAddress);	
		}
		
		gsoad_extcb.SocketAdminList[i].RemotePort = htons(gSoAdConfig.SocketConnection[i].SocketRemotePort);
		gsoad_extcb.SocketAdminList[i].SocketHandle = -1;
		gsoad_extcb.SocketAdminList[i].ConnectionHandle = -1;

		gsoad_extcb.SocketAdminList[i].SocketProtocolIsTcp = FALSE;
		if (gSoAdConfig.SocketConnection[i].SocketProtocol == SOAD_SOCKET_PROT_TCP)
		{
			gsoad_extcb.SocketAdminList[i].SocketProtocolIsTcp = TRUE;
			gsoad_extcb.SocketAdminList[i].TcpinitialInactivityTimer = 0;
		}
		else if (gSoAdConfig.SocketConnection[i].SocketProtocol != SOAD_SOCKET_PROT_UDP)
		{
			// Configuration error!!!
			//FK_TRACE_ERROR("SOAD_INIT_ID, SOAD_E_CONFIG_INVALID \r\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_INIT_ID, SOAD_E_CONFIG_INVALID \n");
		}

		// Check if several connections are expected on this port.
		for (j = 0; j < i; j++)
		{
			if ((gSoAdConfig.SocketConnection[i].SocketProtocol == gSoAdConfig.SocketConnection[j].SocketProtocol) \
					&& (gSoAdConfig.SocketConnection[i].SocketLocalPort == gSoAdConfig.SocketConnection[j].SocketLocalPort))
			{
				gsoad_extcb.SocketAdminList[i].SocketState = SOCKET_DUPLICATE;
				break;
			}
		}
	}

	// Cross reference from gsoad_extcb.SocketAdminList to SocketRoute
	for (i = 0; i < SOAD_SOCKET_ROUTE_COUNT; i++)
	{
		if (gSoAdConfig.SocketRoute[i].SourceSocketRef != NULL)
		{
			if (gSoAdConfig.SocketRoute[i].SourceSocketRef->SocketId < SOAD_SOCKET_COUNT)
			{
				gsoad_extcb.SocketAdminList[gSoAdConfig.SocketRoute[i].SourceSocketRef->SocketId].SocketRouteRef = &gSoAdConfig.SocketRoute[i];
			}
			else
			{
				//FK_TRACE_ERROR("SOAD_INIT_ID, SOAD_E_CONFIG_INVALID \r\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_ERROR,"SOAD_INIT_ID, SOAD_E_CONFIG_INVALID\n");
			}
		}
		else
		{
			//FK_TRACE_WARN("SOAD_INIT_ID, SOAD_E_CONFIG_INVALID \r\n");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_WARN,"SOAD_INIT_ID, SOAD_E_CONFIG_INVALID \r\n");
		}
	}

	// Initialize PduStatus of sgsoad_cb.PduAdminList
	for (i = 0; i < SOAD_PDU_ROUTE_COUNT; i++)
	{
		gsoad_extcb.PduAdminList[i].PduStatus = PDU_IDLE;
	}

	DoIP_Init();

	sgsoad_cb.ModuleStatus = SOAD_INITIALIZED;
}

/** add by fly,20220903 **/
/**********************************************************
 * 函数名称:	SoAd_DeInit
 * 函数作用:	socket adapter套接字适配器层信息去初始化
 * 函数参数:无
 * 函数返回:成功返回0，失败返回小于0
**********************************************************/
static int SoAd_DeInit(void)
{
	int sockNr,release_i = 0,NoRelease_fg = 0;
	
	/** close ready and read status **/
	//FK_TRACE_INFO("start Exit\n");
	SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"start Exit\n");
	for (sockNr = 0; sockNr < SOAD_SOCKET_COUNT; sockNr++)
	{
		//FK_TRACE_INFO("SocketClose, SocketState: %d\n", gsoad_extcb.SocketAdminList[sockNr].SocketState);
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"SocketClose, SocketState: %d\n", gsoad_extcb.SocketAdminList[sockNr].SocketState);
		switch (gsoad_extcb.SocketAdminList[sockNr].SocketState)
		{
			case SOCKET_UDP_READY:
			case SOCKET_TCP_LISTENING:
			{
				shutdown(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, SHUT_RDWR);
				#if 1
				struct epoll_event event;
				event.events = EPOLLIN;  // 监听可读事件（可选 EPOLLOUT、EPOLLET 等）
				event.data.fd = gsoad_extcb.SocketAdminList[sockNr].SocketHandle;  // 关联的文件描述符
				if (epoll_ctl(sgsoad_cb.SocketSelect.epollfd, EPOLL_CTL_DEL, gsoad_extcb.SocketAdminList[sockNr].SocketHandle, &event) == -1) {
					perror("epoll_ctl_4");
					//close(sgsoad_cb.SocketSelect.epollfd);
					//exit(EXIT_FAILURE);
				}
				#endif
				//FD_CLR(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, &sgsoad_cb.SocketSelect.rset);
				close(gsoad_extcb.SocketAdminList[sockNr].SocketHandle);
				
				gsoad_extcb.SocketAdminList[sockNr].SocketHandle = -1;
				gsoad_extcb.SocketAdminList[sockNr].SocketState = SOCKET_INIT;

				break;
			}
			case SOCKET_TCP_READY:
			{
				//FK_TRACE_INFO("tcp ready SocketClose\r\n");
				SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"tcp ready SocketClose\r\n");
				/* 调用上层回调函数释放诊断通道 */
				if (gsoad_extcb.SocketAdminList[sockNr].SocketConnectionRef->SocketLocalPort == 13400)
				{
					/* 只有tcp通道均显示为释放，才最终释放以太网通道 */
					for(release_i=0;release_i < SOAD_SOCKET_COUNT;release_i++){
						if((gsoad_extcb.SocketAdminList[release_i].SocketProtocolIsTcp) && (gsoad_extcb.SocketAdminList[release_i].SocketState == SOCKET_TCP_READY)){
							NoRelease_fg = 1;
						}
					}
					if(NoRelease_fg == 0){
						//FK_TRACE_INFO("channel reset\n");
						SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"channel reset\n");
						if(gsoad_extcb.SetDgsChnStatus != NULL){
							gsoad_extcb.SetDgsChnStatus(0x02);
						}
					}
				}

				shutdown(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, SHUT_RDWR);
				#if 1
				struct epoll_event event;
				event.events = EPOLLIN;  // 监听可读事件（可选 EPOLLOUT、EPOLLET 等）
				event.data.fd = gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle;  // 关联的文件描述符
				if (epoll_ctl(sgsoad_cb.SocketSelect.epollfd, EPOLL_CTL_DEL, gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, &event) == -1) {
					perror("epoll_ctl_4");
					//close(sgsoad_cb.SocketSelect.epollfd);
					//exit(EXIT_FAILURE);
				}
				#endif
				//FD_CLR(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle, &sgsoad_cb.SocketSelect.rset);
				close(gsoad_extcb.SocketAdminList[sockNr].ConnectionHandle);
				

				shutdown(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, SHUT_RDWR);
				#if 1
				event.events = EPOLLIN;  // 监听可读事件（可选 EPOLLOUT、EPOLLET 等）
				event.data.fd = gsoad_extcb.SocketAdminList[sockNr].SocketHandle;  // 关联的文件描述符
				if (epoll_ctl(sgsoad_cb.SocketSelect.epollfd, EPOLL_CTL_DEL, gsoad_extcb.SocketAdminList[sockNr].SocketHandle, &event) == -1) {
					perror("epoll_ctl_4");
					//close(sgsoad_cb.SocketSelect.epollfd);
					//exit(EXIT_FAILURE);
				}
				#endif
				//FD_CLR(gsoad_extcb.SocketAdminList[sockNr].SocketHandle, &sgsoad_cb.SocketSelect.rset);
				close(gsoad_extcb.SocketAdminList[sockNr].SocketHandle);
				
				gsoad_extcb.SocketAdminList[sockNr].SocketHandle = -1;
				gsoad_extcb.SocketAdminList[sockNr].SocketState = SOCKET_INIT;

				break;
			}
			default:
			{
				break;
			}
		}		
	}
		
	DoIP_DeInit();
	//FK_TRACE_INFO("end Exit\n");
	SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"end Exit\n");
	return 0;
}

/**********************************************************
 * 函数名称:	SoAd_Shutdown
 * 函数作用:	SoAd层模块关闭
 * 函数参数:	无
 * 函数返回:	成功返回0，失败返回1
**********************************************************/
/** @req SOAD092 */
Std_ReturnType SoAd_Shutdown(void)
{
	return E_OK;
}

/**********************************************************
 * 函数名称:	SoAd_Cbk_LocalIpAssignmentChg
 * 函数作用:	SoAd本地IP地址变化声明
 * 函数参数:	[in] Index:索引
 *          [in] Valid:是否有效
 *          [in] arg:地址参数
 * 函数返回:无
**********************************************************/
/** @req SOAD209 */
void SoAd_Cbk_LocalIpAssignmentChg( uint8 Index, boolean Valid, struct sockaddr_in* arg)
{
	// TODO: use Index (and make sure it's valid)
	if (TRUE == Valid)
	{
		// Link is up.
		// Start timer so that we can send out vehicle announcements.
		sgsoad_cb.LinkStatus = SOAD_ARC_LINKUP;

#if SOAD_DOIP_ACTIVE == STD_ON
		DoIP_LocalIpAddrAssignmentChg((SoAd_SoConIdType)Index, TCPIP_IPADDR_STATE_ASSIGNED);
#endif
	}
    else
	{
		// Stop timer so that we can stop sending out vehicle announcements.
		sgsoad_cb.LinkStatus = SOAD_ARC_LINKDOWN;

#if SOAD_DOIP_ACTIVE == STD_ON
		DoIP_LocalIpAddrAssignmentChg((SoAd_SoConIdType)Index, TCPIP_IPADDR_STATE_UNASSIGNED);
#endif
	}
}

/** @req SOAD127 */
/**********************************************************
 * 函数名称:	SoAd_SocketReset
 * 函数作用:	SoAd模块套接字复位
 * 函数参数:	无
 * 函数返回:无
**********************************************************/
void SoAd_SocketReset(void)
{

}

#if  0
/** @req SOAD091 */  //using in PduR_ARC_RouteTransmit   send socket without pack doip or other tp
Std_ReturnType SoAdIf_Transmit(uint16 socketNr, const PduInfoType* SoAdSrcPduInfoPtr)
{
	Std_ReturnType returnCode = E_OK;

	if ((gsoad_extcb.SocketAdminList[socketNr].SocketState == SOCKET_TCP_LISTENING) \
			|| (gsoad_extcb.SocketAdminList[socketNr].SocketState == SOCKET_TCP_READY) \
			|| (gsoad_extcb.SocketAdminList[socketNr].SocketState == SOCKET_UDP_READY))
	{
		SoAd_SendIpMessage(socketNr, SoAdSrcPduInfoPtr->SduLength, SoAdSrcPduInfoPtr->SduDataPtr);
	}
	else
	{
		/* Socket not ready */
		returnCode = E_NOT_OK;
	}

	return returnCode;
}

/** @req SOAD105 *///using in PduR_ARC_RouteTransmit  send doip  message
//SoAdSrcPduId 0-uds
Std_ReturnType SoAdTp_Transmit(uint16 socketNr, const PduInfoType* SoAdSrcPduInfoPtr)
{
	Std_ReturnType returnCode = E_OK;
	returnCode = DoIP_HandleTpTransmit(socketNr, SoAdSrcPduInfoPtr);
	return returnCode;
}
#endif

/** @req SOAD091 */
/**********************************************************
 * 函数名称:	SoAdIf_Transmit
 * 函数作用:	SoAd模块透传以太网数据
 * 函数参数:	[in] socketNr:套接字句柄数组索引
 * 函数参数:	[in] SoAdSrcPduInfoPtr:透传的数据单元
 * 函数返回:	成功返回0，失败返回小于0
**********************************************************/
Std_ReturnType SoAdIf_Transmit(uint16 socketNr, const PduInfoType* SoAdSrcPduInfoPtr)
{
	uint16 sendByte = 0;
	Std_ReturnType returnCode = E_OK;

	sendByte = sendByte;				/** delete warning **/
	
	if ((gsoad_extcb.SocketAdminList[socketNr].SocketState == SOCKET_TCP_LISTENING) \
			|| (gsoad_extcb.SocketAdminList[socketNr].SocketState == SOCKET_TCP_READY) \
			|| (gsoad_extcb.SocketAdminList[socketNr].SocketState == SOCKET_UDP_READY))
	{
		sendByte = SoAd_SendIpMessage(socketNr, SoAdSrcPduInfoPtr->SduLength, SoAdSrcPduInfoPtr->SduDataPtr, 0);

		switch (gSoAdConfig.SocketConnection[socketNr].AutosarConnectorType)
		{
#if defined(USE_UDPNM)
			case SOAD_AUTOSAR_CONNECTOR_UDPNM:
			{
				if (sendByte == 0)
				{
					return;
				}
				UdpNm_SoAdIfTxConfirmation(socketNr);
				break;
			}
#endif
			case SOAD_AUTOSAR_CONNECTOR_DOIP:
			{
				break;
			}
			default:
			{
				break;
			}
		}
	}
	else
	{
		/* Socket not ready */
		returnCode = E_NOT_OK;
	}

	return returnCode;
}

//use socket instead of pdu
/**********************************************************
 * 函数名称:	SoAdTp_Transmit
 * 函数作用:	SoAd模块透传以太网数据
 * 函数参数:	[in] socketNr:套接字句柄数组索引
 * 函数参数:	[in] SoAdSrcPduInfoPtr:透传的数据单元
 * 函数返回:	成功返回0，失败返回小于0
**********************************************************/
Std_ReturnType SoAdTp_Transmit(uint16 socketNr, PduInfoType* SoAdSrcPduInfoPtr)
{
	PduInfoType txPduInfo;
	Std_ReturnType returnCode = E_OK;

	switch (gSoAdConfig.SocketConnection[socketNr].AutosarConnectorType)
	{
#if defined(USE_UDPNM)
		case SOAD_AUTOSAR_CONNECTOR_UDPNM:
#endif
		case SOAD_AUTOSAR_CONNECTOR_PDUR:
		{
			if ((gsoad_extcb.SocketAdminList[socketNr].SocketState == SOCKET_TCP_LISTENING) \
					|| (gsoad_extcb.SocketAdminList[socketNr].SocketState == SOCKET_TCP_READY) \
					|| (gsoad_extcb.SocketAdminList[socketNr].SocketState == SOCKET_UDP_READY))
			{
				SoAd_SendIpMessage(socketNr, txPduInfo.SduLength, txPduInfo.SduDataPtr, 0);
			}
			else
			{
				/* Socket not ready */
				returnCode = E_NOT_OK;
			}

			break;
		}
		case SOAD_AUTOSAR_CONNECTOR_DOIP:
		{
			returnCode = DoIP_HandleTpTransmit(socketNr, SoAdSrcPduInfoPtr);
			break;
		}
		default:
		{
			// Not supported connector type
			returnCode = E_NOT_OK;
			break;
		}
	} /* End of switch */

	return returnCode;
}

/**********************************************************
 * 函数名称:	SoAdTp_Transmit_Dianostic
 * 函数作用:	SoAd模块透传诊断数据
 * 函数参数:	[in] tpsocknr:套接字句柄数组索引
 * 函数返回:	成功返回0，失败返回小于0
**********************************************************/
int SoAdTp_Transmit_Dianostic(uint16 tpsocknr, uint8* ptr, uint16 len)
{
	PduInfoType SoAdSrcPduInfoPtr;
	SoAdSrcPduInfoPtr.SduDataPtr = ptr;
	SoAdSrcPduInfoPtr.SduLength  = len;
	Std_ReturnType ret = SoAdTp_Transmit(tpsocknr, &SoAdSrcPduInfoPtr);
	if(ret == E_OK){
		return 0;
	}else{
		return -1;
	}
}

/**********************************************************
 * 函数名称:	SoAd_mSleepTime
 * 函数作用:	SoAd模块定时休眠函数
 * 函数参数:	[in] tmp_msleep:休眠时间
 * 函数返回:	成功返回0，失败返回小于0
**********************************************************/
static int SoAd_mSleepTime(uint64 tmp_msleep)
{
	uint64          tv_sec, tv_usec;
	struct timeval  tv;
	tv_sec  = (tmp_msleep - 10) / 1000;
	tv_usec = ((tmp_msleep - 10) % 1000) * 1000;
	tv.tv_sec  = tv_sec;
	tv.tv_usec = tv_usec;
	for (;;) {                                                              /* select for timer */
		if (0 == select(0, NULL, NULL, NULL, &tv)) {                        /* normal return */
			break;
		}
		if (tv.tv_sec == 0 && tv.tv_usec == 0) {
			break;
		}
	}
	return 0;
}

void printf_systemtime(char* tmpfg)
{
	struct timespec ts_start;
	clock_gettime(CLOCK_MONOTONIC, &ts_start);
	printf("tmpfg = %s,systime = %lld\n",tmpfg,((sint64)ts_start.tv_sec*1000 + (sint64)(ts_start.tv_nsec)/1000000));
}
/**********************************************************
 * 函数名称:	mainFunctionTimer
 * 函数作用:	SoAd模块下定时器任务处理线程
 * 函数参数:	[in] pdata:私有数据指针
 * 函数返回:	任意类型指针
**********************************************************/
static void *mainFunctionTimer(void* pdata)
{
	usleep(10);

	struct timespec ts_start,ts_end;
	sint64 cpu_time_used = 0;

	int i = 0;
	i = i;
	while (1)
	{
		//printf_systemtime("timer");
		//SoAd_mSleepTime(DOIP_MAINFUNCTION_PERIOD_TIME - 10);
		#if 1
		//i++;
		if (clock_gettime(CLOCK_MONOTONIC, &ts_start) == 0) {
		} else {  
			//DE_SODOIP_PRINTF("clock_gettime");
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"clock_gettime");
			
		}  
		
		if (sgsoad_cb.SoadExitFlag == 2)
		{
			break;
		}
		
		DoIP_MainFunction();
		if (clock_gettime(CLOCK_MONOTONIC, &ts_end) == 0) {
		} else {  
			//DE_SODOIP_PRINTF("clock_gettime");  
			SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"clock_gettime");  
		}  
		
		cpu_time_used = (sint64)ts_end.tv_sec*1000 + (sint64)(ts_end.tv_nsec)/1000000 - ((sint64)ts_start.tv_sec*1000 + (sint64)(ts_start.tv_nsec)/1000000);  
		//if(i%100 == 0){
			//FK_TRACE_INFO("real: %lld\n", cpu_time_used);
		//}
		if(cpu_time_used > 0 && cpu_time_used < DOIP_MAINFUNCTION_PERIOD_TIME){
			SoAd_mSleepTime(DOIP_MAINFUNCTION_PERIOD_TIME - cpu_time_used);
		}else{
			SoAd_mSleepTime(DOIP_MAINFUNCTION_PERIOD_TIME);
		}
		//usleep(DOIP_MAINFUNCTION_PERIOD_TIME * 1000);
		//SoAd_mSleepTime(DOIP_MAINFUNCTION_PERIOD_TIME);
		#endif
	}
	
	//DE_SODOIP_PRINTF("MainFunction timer exit\n");
	SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"MainFunction timer exit\n");
	return NULL;
}

/**********************************************************
 * 函数名称:	SoAd_Doip_Init
 * 函数作用:	SoAd模块与doip相关业务初始化
 * 函数参数:	无
 * 函数返回:	成功返回0, 失败返回小于0
**********************************************************/
int SoAd_Doip_Init(void)
{
	//DE_SODOIP_PRINTF("SoAd_Doip_Init \r\n");
	SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"SoAd_Doip_Init\n");
	int ret = -1,sret = -1;
	if (sgsoad_cb.SoadExitFlag == 1)
	{
		//DE_SODOIP_PRINTF("SoAd_Doip_Init repeat!\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_WARN,"SoAd_Doip_Init repeat!\n");
		return -1;
	}
	sgsoad_cb.SoadExitFlag = 1;

	SoAd_Init();

	pthread_attr_t attr; /** 属性结构体 **/ 
    ret = pthread_attr_init(&attr);   /** 初始化 **/ 
    if(ret != 0){
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_WARN,"%s,%d,attr_init error\n",__func__,__LINE__);
	}
    ret = pthread_attr_setdetachstate(&attr,PTHREAD_CREATE_DETACHED);   /** 设置线程属性为，分离属性 **/ 
    if(ret != 0){
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_WARN,"%s,%d,setdetachstate error\n",__func__,__LINE__);
	}
	
	sret = pthread_create(&sgsoad_cb.mainFunctionthread, &attr, mainFunctionThread, (void *)&ret);  /* 创建linux线程 */
    assert(sret == 0);

	sret = pthread_create(&sgsoad_cb.mainTimerthread, &attr, mainFunctionTimer, (void *)&ret);  /* 创建linux线程 */
    assert(sret == 0);
	
	ret = pthread_attr_destroy(&attr);   /** 只是在创建的时候有用，创建完之后就被销毁了 **/ 
    if(ret != 0){
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_WARN,"%s,%d, attr destroy error\n",__func__,__LINE__);
	}

	#if 0
	fk_thread_create(
			"MainFunction thread",
			NULL,
			mainFunctionThread,
			NULL,
			NULL);
	
	fk_thread_create(
				"MainFunction timer thread",
				NULL,
				mainFunctionTimer,
				NULL,
				NULL);
	#endif
	return 0;
}

/** add by fly,20220903 **/
/**********************************************************
 * 函数名称:	SoAd_Doip_DeInit
 * 函数作用:	SoAd模块去初始化
 * 函数参数:	无
 * 函数返回:	成功返回0, 失败返回-1
**********************************************************/
int SoAd_Doip_DeInit(void)
{
	if (sgsoad_cb.SoadExitFlag == 2)
	{
		//FK_TRACE_WARN("repeat,DeInit Exit\n");
		SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_WARN,"repeat,DeInit Exit\n");
		return -1;
	}
	sgsoad_cb.SoadExitFlag = 2;
	
	usleep(300);
	//SoAd_DeInit();
	usleep(200);
	
	return 0;	
}

/** @req SOAD193 */
void TcpIp_Init(void)
{
    //FK_TRACE_INFO("infor: initialized\r\n");
	SOADDOIP_TRACE_PRINTF(SOADDOIP_DEBUG_INFO,"infor: initialized\r\n");
}

/** @req SOAD194 */
void TcpIp_Shutdown(void)
{

}

/** @req SOAD143 */
void TcpIp_MainFunctionCyclic(void)
{

}

/** @req SOAD196*/
Std_ReturnType TcpIp_SetDhcpHostNameOption(uint8* HostNameOption, uint8 HostNameLen)
{
	return E_OK;
}
